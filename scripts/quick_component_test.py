#!/usr/bin/env python3
"""
Quick Component Test for Signature Verification Fix
Tests core components without network dependencies.
"""

import asyncio
import logging
import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_keypair_loading():
    """Test keypair loading from environment."""
    try:
        from solders.keypair import Keypair
        
        # Try loading from private key in environment
        private_key = os.getenv('WALLET_PRIVATE_KEY')
        if private_key:
            keypair = Keypair.from_base58_string(private_key)
            logger.info(f"✅ Keypair loaded successfully: {keypair.pubkey()}")
            return True, keypair
        else:
            logger.error("❌ No WALLET_PRIVATE_KEY found in environment")
            return False, None
            
    except Exception as e:
        logger.error(f"❌ Error loading keypair: {e}")
        return False, None

async def test_modern_executor_initialization():
    """Test modern executor initialization."""
    try:
        from phase_4_deployment.rpc_execution.modern_transaction_executor import ModernTransactionExecutor
        
        config = {
            'primary_rpc': f"https://mainnet.helius-rpc.com/?api-key={os.getenv('HELIUS_API_KEY')}",
            'fallback_rpc': 'https://api.mainnet-beta.solana.com',
            'jito_rpc': 'https://mainnet.block-engine.jito.wtf/api/v1',
            'helius_api_key': os.getenv('HELIUS_API_KEY')
        }
        
        executor = ModernTransactionExecutor(config)
        await executor.initialize()
        
        logger.info("✅ Modern executor initialized successfully")
        await executor.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Error initializing modern executor: {e}")
        return False

async def test_jupiter_client_initialization():
    """Test Jupiter client initialization."""
    try:
        from phase_4_deployment.utils.modern_jupiter_client import ModernJupiterClient
        
        rpc_url = f"https://mainnet.helius-rpc.com/?api-key={os.getenv('HELIUS_API_KEY')}"
        client = ModernJupiterClient(rpc_url)
        
        logger.info("✅ Jupiter client initialized successfully")
        await client.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Error initializing Jupiter client: {e}")
        return False

async def test_transaction_builder():
    """Test optimized transaction builder."""
    try:
        from phase_4_deployment.utils.modern_jupiter_client import OptimizedTransactionBuilder
        from solders.keypair import Keypair
        
        # Load keypair
        private_key = os.getenv('WALLET_PRIVATE_KEY')
        if not private_key:
            logger.error("❌ No WALLET_PRIVATE_KEY for transaction builder test")
            return False
            
        keypair = Keypair.from_base58_string(private_key)
        rpc_url = f"https://mainnet.helius-rpc.com/?api-key={os.getenv('HELIUS_API_KEY')}"
        
        builder = OptimizedTransactionBuilder(rpc_url, keypair)
        
        logger.info("✅ Transaction builder initialized successfully")
        await builder.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing transaction builder: {e}")
        return False

async def test_imports():
    """Test all required imports."""
    try:
        # Test solders imports
        from solders.transaction import VersionedTransaction
        from solders.keypair import Keypair
        from solders.hash import Hash
        logger.info("✅ Solders imports successful")
        
        # Test httpx import
        import httpx
        logger.info("✅ HTTPX import successful")
        
        # Test base58 import
        import base58
        logger.info("✅ Base58 import successful")
        
        # Test modern components
        from phase_4_deployment.rpc_execution.modern_transaction_executor import ModernTransactionExecutor
        from phase_4_deployment.utils.modern_jupiter_client import ModernJupiterClient, OptimizedTransactionBuilder
        logger.info("✅ Modern component imports successful")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Import error: {e}")
        return False

async def run_quick_test():
    """Run quick component test."""
    print("🧪 QUICK COMPONENT TEST")
    print("=" * 50)
    
    results = {}
    
    # Test 1: Imports
    logger.info("🧪 Test 1: Required Imports")
    results['imports'] = await test_imports()
    
    # Test 2: Keypair Loading
    logger.info("🧪 Test 2: Keypair Loading")
    keypair_success, keypair = await test_keypair_loading()
    results['keypair_loading'] = keypair_success
    
    # Test 3: Modern Executor
    logger.info("🧪 Test 3: Modern Executor Initialization")
    results['modern_executor'] = await test_modern_executor_initialization()
    
    # Test 4: Jupiter Client
    logger.info("🧪 Test 4: Jupiter Client Initialization")
    results['jupiter_client'] = await test_jupiter_client_initialization()
    
    # Test 5: Transaction Builder
    logger.info("🧪 Test 5: Transaction Builder")
    results['transaction_builder'] = await test_transaction_builder()
    
    # Summary
    print("\n📊 TEST RESULTS")
    print("=" * 30)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 30)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("The modern transaction system is ready for integration.")
        print("\nNext steps:")
        print("1. Run: python scripts/integrate_modern_transaction_system.py")
        print("2. Test with: python scripts/modern_live_trading.py --duration 5 --dry-run")
    else:
        print("⚠️ SOME TESTS FAILED")
        print("Please check the error messages above and resolve issues.")
        print("Common fixes:")
        print("- Ensure WALLET_PRIVATE_KEY is set in .env")
        print("- Ensure HELIUS_API_KEY is set in .env")
        print("- Check network connectivity")
    
    return all_passed

async def main():
    """Main test function."""
    try:
        success = await run_quick_test()
        return success
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
