#!/usr/bin/env python3
"""
Consolidate System to 3 Single Entry Points
Removes all redundant files and keeps only:
1. Live Trading: scripts/unified_live_trading.py
2. Backtesting: phase_4_deployment/unified_runner.py --mode backtest
3. Paper Trading: phase_4_deployment/unified_runner.py --mode paper
"""

import os
import shutil
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# Files to DELETE (redundant entry points and components)
REDUNDANT_FILES = [
    # My redundant enhanced components
    "core/transaction/enhanced_tx_builder.py",
    "core/transaction/enhanced_tx_executor.py",
    "scripts/enhanced_live_trading.py",
    "scripts/test_live_orca_fix.py",
    "scripts/test_orca_price_fix.py",
    
    # Redundant live trading entry points
    "scripts/test_fixed_live_trading.py",
    "scripts/run_fixed_live_trading.py",
    "scripts/deploy_live_production.py",
    "scripts/run_48_hour_live_trading.py",
    "scripts/test_live_production_deployment.py",
    "scripts/test_live_trading_fixed.py",
    "scripts/single_live_trade_test.py",
    
    # Phase 4 redundant entry points
    "phase_4_deployment/start_live_trading.py",
    "phase_4_deployment/run_live_integration.py",
    "phase_4_deployment/run_complete_system.py",
    "phase_4_deployment/run_trading_system.py",
    "phase_4_deployment/run_paper_trading.py",
    "phase_4_deployment/run_simulation.py",
    "phase_4_deployment/run_active_simulation.py",
    
    # Phase 4 script redundants
    "phase_4_deployment/scripts/run_live_trade_test.py",
    "phase_4_deployment/scripts/test_real_transaction.py",
    "phase_4_deployment/scripts/test_trade.py",
    "phase_4_deployment/scripts/run_paper_trade.py",
    "phase_4_deployment/scripts/simulation_test.py",
    "phase_4_deployment/scripts/run_simulation_test.py",
    "phase_4_deployment/scripts/verify_simulation.py",
    
    # Redundant test scripts (keep only essential 3)
    "scripts/test_orca_integration.py",
    "scripts/test_end_to_end_system.py",
    "scripts/test_100_percent_ready.py",
    "scripts/test_base64_encoding_fix.py",
    "scripts/test_comprehensive_alerts.py",
    "scripts/test_dashboard_metrics.py",
    "scripts/test_dashboard_performance.py",
    "scripts/test_direct_transaction.py",
    "scripts/test_dual_telegram.py",
    "scripts/test_jito_signature_verification_fix.py",
    "scripts/test_live_alerts_integration.py",
    "scripts/test_risk_components.py",
    "scripts/test_serialization_fix.py",
    "scripts/test_signature_fix.py",
    "scripts/test_simple_transaction.py",
    "scripts/test_telegram_alerts.py",
    "scripts/testnet_system_validation.py",
    
    # Enhanced wrappers
    "scripts/enhanced_dashboard_sync.py",
    "core/notifications/enhanced_telegram_wrapper.py",
    
    # Redundant analysis (keep only essential)
    "scripts/analyze_system_files.py",
    "scripts/wallet_scaling_analysis.py",
    "scripts/compare_strategies.py",
    
    # Redundant deployment
    "scripts/deploy_fixed_jupiter_system.py",
    "phase_4_deployment/deploy.py",
    "phase_4_deployment/update_deployment.py",
    "phase_4_deployment/setup_environment.py",
    
    # One-time cleanup scripts
    "scripts/cleanup_deprecated_files.py",
    "scripts/cleanup_jupiter_files.py",
    "scripts/cleanup_redundant_wallets.py",
    "scripts/create_focused_depr_list.py",
    
    # Fix scripts (one-time use)
    "scripts/fix_jupiter_blockhash_timing.py",
    "scripts/fix_signature_verification.py",
    "scripts/fix_testnet_keypair.py",
    
    # Redundant setup scripts
    "scripts/setup_jupiter_config.py",
    "scripts/setup_testnet_environment.py",
    "scripts/simple_testnet_test.py",
    "scripts/simple_trade_test.py",
    
    # Redundant integration tests
    "scripts/integration_test.py",
    "scripts/system_test.py",
    "scripts/validate_trading_system.py",
    "scripts/verify_simulation.py",
]

# Essential files to KEEP (the 3 entry points + essential support)
ESSENTIAL_FILES = [
    # The 3 entry points
    "scripts/unified_live_trading.py",                    # Live Trading
    "phase_4_deployment/unified_runner.py",              # Backtest + Paper Trading
    
    # Essential tests (keep only 3)
    "scripts/comprehensive_system_test.py",
    "scripts/final_production_verification.py",
    "scripts/system_status_check.py",
    
    # Essential analysis tools
    "scripts/rich_trade_analyzer.py",
    "scripts/analyze_trades.py",
    "scripts/analyze_live_metrics_profitability.py",
    
    # Essential deployment
    "phase_4_deployment/deploy_production.sh",
    "phase_4_deployment/docker_deploy/entrypoint.sh",
    
    # Essential monitoring
    "phase_4_deployment/dashboard/streamlit_dashboard.py",
    "phase_4_deployment/monitoring/health_check_server.py",
]

def backup_files():
    """Create backup of files before deletion."""
    backup_dir = "backups/consolidation_backup"
    os.makedirs(backup_dir, exist_ok=True)
    
    logger.info(f"Creating backup in {backup_dir}...")
    
    for file_path in REDUNDANT_FILES:
        if os.path.exists(file_path):
            backup_path = os.path.join(backup_dir, file_path)
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            shutil.copy2(file_path, backup_path)
    
    logger.info("✅ Backup completed")

def delete_redundant_files():
    """Delete redundant files."""
    deleted_count = 0
    not_found_count = 0
    
    logger.info("🗑️ Deleting redundant files...")
    
    for file_path in REDUNDANT_FILES:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"✅ Deleted: {file_path}")
                deleted_count += 1
            except Exception as e:
                logger.error(f"❌ Failed to delete {file_path}: {e}")
        else:
            logger.debug(f"⚠️ Not found: {file_path}")
            not_found_count += 1
    
    logger.info(f"📊 Deletion summary: {deleted_count} deleted, {not_found_count} not found")

def update_depr_txt():
    """Update depr.txt with consolidated information."""
    depr_content = """# SYSTEM CONSOLIDATED TO 3 ENTRY POINTS
# All redundant files have been removed

# === 3 SINGLE ENTRY POINTS ===
# 1. Live Trading: scripts/unified_live_trading.py
# 2. Backtesting: phase_4_deployment/unified_runner.py --mode backtest  
# 3. Paper Trading: phase_4_deployment/unified_runner.py --mode paper

# === DELETED REDUNDANT FILES ===
# All files listed below have been removed from the system
# to eliminate confusion and maintain clean architecture

"""
    
    for file_path in REDUNDANT_FILES:
        depr_content += f"# {file_path}\n"
    
    depr_content += """
# === ESSENTIAL FILES KEPT ===
# These are the only entry points and essential support files

"""
    
    for file_path in ESSENTIAL_FILES:
        depr_content += f"# {file_path}\n"
    
    with open("depr.txt", "w") as f:
        f.write(depr_content)
    
    logger.info("✅ Updated depr.txt with consolidation information")

def create_usage_guide():
    """Create usage guide for the 3 entry points."""
    guide_content = """# 🎯 SYNERGY7 SYSTEM - 3 ENTRY POINTS USAGE GUIDE

## **SYSTEM CONSOLIDATED TO 3 SINGLE ENTRY POINTS**

### **1. 🔴 LIVE TRADING**
```bash
# Live trading with real money
cd /Users/<USER>/HedgeFund
DRY_RUN=false TRADING_ENABLED=true python3 scripts/unified_live_trading.py --duration 60
```

### **2. 📊 BACKTESTING**
```bash
# Historical backtesting
cd /Users/<USER>/HedgeFund
python3 phase_4_deployment/unified_runner.py --mode backtest
```

### **3. 📝 PAPER TRADING**
```bash
# Paper trading simulation
cd /Users/<USER>/HedgeFund
python3 phase_4_deployment/unified_runner.py --mode paper
```

## **ESSENTIAL SUPPORT COMMANDS**

### **Dashboard**
```bash
streamlit run phase_4_deployment/dashboard/streamlit_dashboard.py
```

### **System Health**
```bash
python3 phase_4_deployment/monitoring/health_check_server.py
```

### **Trade Analysis**
```bash
python3 scripts/rich_trade_analyzer.py
```

### **System Tests**
```bash
python3 scripts/comprehensive_system_test.py
```

## **ALL OTHER ENTRY POINTS REMOVED**
- No more confusion with multiple entry points
- No more redundant scripts
- Clean, focused architecture
- Single source of truth for each function
"""
    
    with open("USAGE_GUIDE.md", "w") as f:
        f.write(guide_content)
    
    logger.info("✅ Created USAGE_GUIDE.md")

def main():
    """Main consolidation function."""
    logger.info("🚀 Starting system consolidation to 3 entry points...")
    
    # Create backup
    backup_files()
    
    # Delete redundant files
    delete_redundant_files()
    
    # Update documentation
    update_depr_txt()
    create_usage_guide()
    
    logger.info("🎉 System consolidation completed!")
    logger.info("📋 Check USAGE_GUIDE.md for the 3 entry points")
    logger.info("🔍 Check depr.txt for deleted files list")
    
    print("\n" + "="*60)
    print("🎯 SYSTEM CONSOLIDATED TO 3 ENTRY POINTS")
    print("="*60)
    print("1. Live Trading: scripts/unified_live_trading.py")
    print("2. Backtesting: phase_4_deployment/unified_runner.py --mode backtest")
    print("3. Paper Trading: phase_4_deployment/unified_runner.py --mode paper")
    print("="*60)

if __name__ == "__main__":
    main()
