#!/usr/bin/env python3
"""
Quick Test for Signature Verification Fix
Tests the modern transaction system with a minimal transaction.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

async def quick_test():
    """Run quick test of signature verification fix."""
    try:
        from scripts.resolve_signature_verification_failures import SignatureVerificationResolver
        
        print("🧪 QUICK SIGNATURE VERIFICATION TEST")
        print("=" * 50)
        
        resolver = SignatureVerificationResolver()
        
        # Run comprehensive test
        results = await resolver.run_comprehensive_test()
        
        if results.get('overall_success'):
            print("\n🎉 SUCCESS: Signature verification issues resolved!")
            return True
        else:
            print("\n❌ FAILED: Some issues remain")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(quick_test())
    sys.exit(0 if success else 1)
