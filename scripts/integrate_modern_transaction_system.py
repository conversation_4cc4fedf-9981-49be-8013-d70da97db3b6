#!/usr/bin/env python3
"""
Integration Script for Modern Transaction System
Updates existing trading system to use modern transaction execution.
"""

import asyncio
import logging
import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ModernTransactionIntegrator:
    """Integrates modern transaction system into existing trading infrastructure."""
    
    def __init__(self):
        """Initialize the integrator."""
        self.project_root = project_root
        self.backup_dir = project_root / 'backups' / 'pre_modern_integration'
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    async def backup_existing_system(self):
        """Backup existing transaction system files."""
        try:
            import shutil
            
            files_to_backup = [
                'scripts/unified_live_trading.py',
                'phase_4_deployment/rpc_execution/transaction_executor.py',
                'phase_4_deployment/rpc_execution/jito_executor.py',
                'phase_4_deployment/utils/jupiter_swap_fallback.py'
            ]
            
            logger.info("📦 Backing up existing system files...")
            
            for file_path in files_to_backup:
                source = self.project_root / file_path
                if source.exists():
                    dest = self.backup_dir / file_path
                    dest.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(source, dest)
                    logger.info(f"✅ Backed up: {file_path}")
            
            logger.info(f"✅ Backup completed: {self.backup_dir}")
            
        except Exception as e:
            logger.error(f"❌ Error backing up system: {e}")
            raise

    async def create_modern_trading_script(self):
        """Create modern trading script with signature verification fixes."""
        try:
            modern_script_content = '''#!/usr/bin/env python3
"""
Modern Live Trading with Signature Verification Fixes
Uses modern transaction execution to eliminate signature verification failures.
"""

import asyncio
import logging
import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv
from solders.keypair import Keypair

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ModernLiveTrader:
    """Modern live trader with signature verification fixes."""
    
    def __init__(self):
        """Initialize modern live trader."""
        self.config = self._load_config()
        self.keypair = self._load_keypair()
        self.modern_executor = None
        self.jupiter_client = None
        self.signal_generator = None
        
    def _load_config(self):
        """Load trading configuration."""
        return {
            'primary_rpc': f"https://mainnet.helius-rpc.com/?api-key={os.getenv('HELIUS_API_KEY')}",
            'fallback_rpc': 'https://api.mainnet-beta.solana.com',
            'jito_rpc': 'https://mainnet.block-engine.jito.wtf/api/v1',
            'helius_api_key': os.getenv('HELIUS_API_KEY'),
            'wallet_address': os.getenv('WALLET_ADDRESS'),
            'keypair_path': os.getenv('KEYPAIR_PATH', 'wallet/trading_wallet_keypair.json'),
            'dry_run': os.getenv('DRY_RUN', 'false').lower() == 'true',
            'max_trades_per_hour': int(os.getenv('MAX_TRADES_PER_HOUR', '3')),
            'min_confidence_threshold': float(os.getenv('MIN_CONFIDENCE_THRESHOLD', '0.8'))
        }
    
    def _load_keypair(self):
        """Load keypair from environment or file."""
        try:
            private_key = os.getenv('WALLET_PRIVATE_KEY')
            if private_key:
                return Keypair.from_base58_string(private_key)
            
            keypair_path = self.config.get('keypair_path')
            if keypair_path and os.path.exists(keypair_path):
                with open(keypair_path, 'r') as f:
                    keypair_data = json.load(f)
                return Keypair.from_bytes(keypair_data)
            
            raise ValueError("No valid keypair found")
            
        except Exception as e:
            logger.error(f"❌ Error loading keypair: {e}")
            raise

    async def initialize(self):
        """Initialize modern trading components."""
        try:
            # Import modern components
            from phase_4_deployment.rpc_execution.modern_transaction_executor import ModernTransactionExecutor
            from phase_4_deployment.utils.modern_jupiter_client import OptimizedTransactionBuilder
            from phase_4_deployment.signal_generator.signal_generator import SignalGenerator
            
            # Initialize modern executor
            self.modern_executor = ModernTransactionExecutor(self.config)
            await self.modern_executor.initialize()
            
            # Initialize Jupiter client
            self.jupiter_client = OptimizedTransactionBuilder(
                rpc_url=self.config['primary_rpc'],
                keypair=self.keypair
            )
            
            # Initialize signal generator
            self.signal_generator = SignalGenerator()
            
            logger.info("✅ Modern trading components initialized")
            
        except Exception as e:
            logger.error(f"❌ Error initializing components: {e}")
            raise

    async def run_trading_cycle(self):
        """Run a single trading cycle with modern execution."""
        try:
            # Generate signals
            logger.info("📡 Generating trading signals...")
            signals = await self.signal_generator.generate_signals()
            
            if not signals:
                logger.info("📊 No signals generated")
                return {'signals_generated': 0, 'trade_executed': False}
            
            # Select best signal
            best_signal = max(signals, key=lambda s: s.get('confidence', 0))
            
            if best_signal.get('confidence', 0) < self.config['min_confidence_threshold']:
                logger.info(f"📊 Best signal confidence {best_signal.get('confidence', 0)} below threshold {self.config['min_confidence_threshold']}")
                return {'signals_generated': len(signals), 'trade_executed': False}
            
            logger.info(f"🎯 Selected signal: {best_signal['market']} {best_signal['action']} (confidence: {best_signal.get('confidence', 0)})")
            
            # Build optimized transaction
            logger.info("🔨 Building optimized transaction...")
            tx_result = await self.jupiter_client.build_swap_transaction(best_signal)
            
            if not tx_result or not tx_result.get('success'):
                logger.error("❌ Failed to build transaction")
                return {'signals_generated': len(signals), 'trade_executed': False, 'error': 'Transaction building failed'}
            
            # Execute with modern executor
            logger.info("🚀 Executing transaction with modern executor...")
            execution_result = await self.modern_executor.execute_transaction_with_bundles(
                tx_result['transaction'],
                {'skip_preflight': False, 'max_retries': 3}
            )
            
            if execution_result.get('success'):
                logger.info(f"✅ Trade executed successfully!")
                logger.info(f"   Provider: {execution_result.get('provider')}")
                logger.info(f"   Signature: {execution_result.get('signature')}")
                
                return {
                    'signals_generated': len(signals),
                    'trade_executed': True,
                    'execution_result': execution_result,
                    'signal': best_signal
                }
            else:
                logger.error(f"❌ Trade execution failed: {execution_result.get('error')}")
                return {
                    'signals_generated': len(signals),
                    'trade_executed': False,
                    'error': execution_result.get('error')
                }
                
        except Exception as e:
            logger.error(f"❌ Error in trading cycle: {e}")
            return {'signals_generated': 0, 'trade_executed': False, 'error': str(e)}

    async def run_live_trading(self, duration_minutes: float = 30.0):
        """Run live trading session."""
        try:
            logger.info(f"🚀 Starting modern live trading session for {duration_minutes} minutes")
            
            await self.initialize()
            
            start_time = asyncio.get_event_loop().time()
            end_time = start_time + (duration_minutes * 60)
            
            cycle_count = 0
            successful_trades = 0
            total_signals = 0
            
            while asyncio.get_event_loop().time() < end_time:
                cycle_count += 1
                logger.info(f"🔄 Trading cycle {cycle_count}")
                
                # Run trading cycle
                result = await self.run_trading_cycle()
                
                total_signals += result.get('signals_generated', 0)
                if result.get('trade_executed'):
                    successful_trades += 1
                
                # Wait before next cycle
                await asyncio.sleep(60)  # 1 minute between cycles
            
            # Final summary
            logger.info(f"📊 Trading session completed:")
            logger.info(f"   Cycles: {cycle_count}")
            logger.info(f"   Total signals: {total_signals}")
            logger.info(f"   Successful trades: {successful_trades}")
            
            # Get executor metrics
            metrics = await self.modern_executor.get_metrics()
            logger.info(f"📈 Executor metrics: {metrics}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error in live trading session: {e}")
            return False
        
        finally:
            await self.close()

    async def close(self):
        """Clean up resources."""
        if self.modern_executor:
            await self.modern_executor.close()
        if self.jupiter_client:
            await self.jupiter_client.close()


async def main():
    """Main function for modern live trading."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Modern Live Trading System")
    parser.add_argument("--duration", type=float, default=30.0, help="Trading duration in minutes")
    parser.add_argument("--dry-run", action="store_true", help="Run in dry-run mode")
    
    args = parser.parse_args()
    
    if args.dry_run:
        os.environ['DRY_RUN'] = 'true'
    
    print("🚀 MODERN LIVE TRADING SYSTEM")
    print("=" * 60)
    print("✅ Signature verification failures resolved")
    print("✅ Jito Bundle execution enabled")
    print("✅ Fresh blockhash handling implemented")
    print("=" * 60)
    
    trader = ModernLiveTrader()
    success = await trader.run_live_trading(args.duration)
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
'''
            
            # Save modern trading script
            modern_script_path = self.project_root / 'scripts' / 'modern_live_trading.py'
            with open(modern_script_path, 'w') as f:
                f.write(modern_script_content)
            
            # Make executable
            os.chmod(modern_script_path, 0o755)
            
            logger.info(f"✅ Modern trading script created: {modern_script_path}")
            
        except Exception as e:
            logger.error(f"❌ Error creating modern trading script: {e}")
            raise

    async def update_unified_runner(self):
        """Update unified runner to use modern transaction system."""
        try:
            unified_runner_path = self.project_root / 'phase_4_deployment' / 'unified_runner.py'
            
            if not unified_runner_path.exists():
                logger.warning(f"⚠️ Unified runner not found: {unified_runner_path}")
                return
            
            # Read existing content
            with open(unified_runner_path, 'r') as f:
                content = f.read()
            
            # Add modern transaction import
            modern_import = '''
# Modern transaction system imports
try:
    from rpc_execution.modern_transaction_executor import ModernTransactionExecutor
    from utils.modern_jupiter_client import OptimizedTransactionBuilder
    MODERN_TRANSACTION_AVAILABLE = True
    logger.info("✅ Modern transaction system available")
except ImportError as e:
    MODERN_TRANSACTION_AVAILABLE = False
    logger.warning(f"⚠️ Modern transaction system not available: {e}")
'''
            
            # Insert after existing imports
            if 'from rpc_execution.modern_transaction_executor import' not in content:
                import_section_end = content.find('\nlogger = logging.getLogger(__name__)')
                if import_section_end != -1:
                    content = content[:import_section_end] + modern_import + content[import_section_end:]
                    
                    # Write updated content
                    with open(unified_runner_path, 'w') as f:
                        f.write(content)
                    
                    logger.info(f"✅ Updated unified runner: {unified_runner_path}")
                else:
                    logger.warning("⚠️ Could not find insertion point in unified runner")
            else:
                logger.info("✅ Unified runner already has modern transaction imports")
                
        except Exception as e:
            logger.error(f"❌ Error updating unified runner: {e}")

    async def create_quick_test_script(self):
        """Create a quick test script to verify the fix."""
        try:
            test_script_content = '''#!/usr/bin/env python3
"""
Quick Test for Signature Verification Fix
Tests the modern transaction system with a minimal transaction.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

async def quick_test():
    """Run quick test of signature verification fix."""
    try:
        from scripts.resolve_signature_verification_failures import SignatureVerificationResolver
        
        print("🧪 QUICK SIGNATURE VERIFICATION TEST")
        print("=" * 50)
        
        resolver = SignatureVerificationResolver()
        
        # Run comprehensive test
        results = await resolver.run_comprehensive_test()
        
        if results.get('overall_success'):
            print("\\n🎉 SUCCESS: Signature verification issues resolved!")
            return True
        else:
            print("\\n❌ FAILED: Some issues remain")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(quick_test())
    sys.exit(0 if success else 1)
'''
            
            test_script_path = self.project_root / 'scripts' / 'quick_signature_test.py'
            with open(test_script_path, 'w') as f:
                f.write(test_script_content)
            
            os.chmod(test_script_path, 0o755)
            
            logger.info(f"✅ Quick test script created: {test_script_path}")
            
        except Exception as e:
            logger.error(f"❌ Error creating test script: {e}")

    async def run_integration(self):
        """Run the complete integration process."""
        try:
            logger.info("🔧 Starting modern transaction system integration...")
            
            # Step 1: Backup existing system
            await self.backup_existing_system()
            
            # Step 2: Create modern trading script
            await self.create_modern_trading_script()
            
            # Step 3: Update unified runner
            await self.update_unified_runner()
            
            # Step 4: Create quick test script
            await self.create_quick_test_script()
            
            logger.info("✅ Integration completed successfully!")
            
            print("\n🎉 INTEGRATION COMPLETE")
            print("=" * 40)
            print("Next steps:")
            print("1. Run: python scripts/quick_signature_test.py")
            print("2. If test passes, run: python scripts/modern_live_trading.py --duration 5 --dry-run")
            print("3. For live trading: python scripts/modern_live_trading.py --duration 30")
            print("=" * 40)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Integration failed: {e}")
            return False


async def main():
    """Main integration function."""
    print("🔧 MODERN TRANSACTION SYSTEM INTEGRATOR")
    print("=" * 60)
    print("This script integrates the modern transaction system")
    print("to resolve signature verification failures.")
    print("=" * 60)
    
    integrator = ModernTransactionIntegrator()
    success = await integrator.run_integration()
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
