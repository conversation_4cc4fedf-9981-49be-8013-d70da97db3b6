#!/usr/bin/env python3
"""
Modern Live Trading with Signature Verification Fixes
Uses modern transaction execution to eliminate signature verification failures.
"""

import asyncio
import logging
import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv
from solders.keypair import Keypair

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ModernLiveTrader:
    """Modern live trader with signature verification fixes."""

    def __init__(self):
        """Initialize modern live trader."""
        self.config = self._load_config()
        self.keypair = self._load_keypair()
        self.modern_executor = None
        self.jupiter_client = None
        self.signal_generator = None

    def _load_config(self):
        """Load trading configuration."""
        return {
            'primary_rpc': f"https://mainnet.helius-rpc.com/?api-key={os.getenv('HELIUS_API_KEY')}",
            'fallback_rpc': 'https://api.mainnet-beta.solana.com',
            'jito_rpc': 'https://mainnet.block-engine.jito.wtf/api/v1',
            'helius_api_key': os.getenv('HELIUS_API_KEY'),
            'wallet_address': os.getenv('WALLET_ADDRESS'),
            'keypair_path': os.getenv('KEYPAIR_PATH', 'wallet/trading_wallet_keypair.json'),
            'dry_run': os.getenv('DRY_RUN', 'false').lower() == 'true',
            'max_trades_per_hour': int(os.getenv('MAX_TRADES_PER_HOUR', '3')),
            'min_confidence_threshold': float(os.getenv('MIN_CONFIDENCE_THRESHOLD', '0.8'))
        }

    def _load_keypair(self):
        """Load keypair from environment or file."""
        try:
            private_key = os.getenv('WALLET_PRIVATE_KEY')
            if private_key:
                return Keypair.from_base58_string(private_key)

            keypair_path = self.config.get('keypair_path')
            if keypair_path and os.path.exists(keypair_path):
                with open(keypair_path, 'r') as f:
                    keypair_data = json.load(f)
                return Keypair.from_bytes(keypair_data)

            raise ValueError("No valid keypair found")

        except Exception as e:
            logger.error(f"❌ Error loading keypair: {e}")
            raise

    async def initialize(self):
        """Initialize modern trading components."""
        try:
            # Import modern components
            from phase_4_deployment.rpc_execution.modern_transaction_executor import ModernTransactionExecutor
            from phase_4_deployment.utils.modern_jupiter_client import OptimizedTransactionBuilder

            # Initialize modern executor
            self.modern_executor = ModernTransactionExecutor(self.config)
            await self.modern_executor.initialize()

            # Initialize Jupiter client
            self.jupiter_client = OptimizedTransactionBuilder(
                rpc_url=self.config['primary_rpc'],
                keypair=self.keypair
            )

            # Initialize signal generator with config
            signal_config = {
                'market_microstructure': {'markets': ['SOL/USDC']},
                'strategies': [
                    {'name': 'momentum', 'type': 'momentum'},
                    {'name': 'mean_reversion', 'type': 'mean_reversion'}
                ],
                'signal_generation': {'update_interval_ms': 1000, 'publish_interval_ms': 1000}
            }
            self.signal_generator = SignalGenerator(signal_config)

            logger.info("✅ Modern trading components initialized")

        except Exception as e:
            logger.error(f"❌ Error initializing components: {e}")
            raise

    async def run_trading_cycle(self):
        """Run a single trading cycle with modern execution."""
        try:
            # Generate signals
            logger.info("📡 Generating trading signals...")
            signals = await self.signal_generator.generate_signals()

            if not signals:
                logger.info("📊 No signals generated")
                return {'signals_generated': 0, 'trade_executed': False}

            # Select best signal
            best_signal = max(signals, key=lambda s: s.get('confidence', 0))

            if best_signal.get('confidence', 0) < self.config['min_confidence_threshold']:
                logger.info(f"📊 Best signal confidence {best_signal.get('confidence', 0)} below threshold {self.config['min_confidence_threshold']}")
                return {'signals_generated': len(signals), 'trade_executed': False}

            logger.info(f"🎯 Selected signal: {best_signal['market']} {best_signal['action']} (confidence: {best_signal.get('confidence', 0)})")

            # Build optimized transaction
            logger.info("🔨 Building optimized transaction...")
            tx_result = await self.jupiter_client.build_swap_transaction(best_signal)

            if not tx_result or not tx_result.get('success'):
                logger.error("❌ Failed to build transaction")
                return {'signals_generated': len(signals), 'trade_executed': False, 'error': 'Transaction building failed'}

            # Execute with modern executor
            logger.info("🚀 Executing transaction with modern executor...")
            execution_result = await self.modern_executor.execute_transaction_with_bundles(
                tx_result['transaction'],
                {'skip_preflight': False, 'max_retries': 3}
            )

            if execution_result.get('success'):
                logger.info(f"✅ Trade executed successfully!")
                logger.info(f"   Provider: {execution_result.get('provider')}")
                logger.info(f"   Signature: {execution_result.get('signature')}")

                return {
                    'signals_generated': len(signals),
                    'trade_executed': True,
                    'execution_result': execution_result,
                    'signal': best_signal
                }
            else:
                logger.error(f"❌ Trade execution failed: {execution_result.get('error')}")
                return {
                    'signals_generated': len(signals),
                    'trade_executed': False,
                    'error': execution_result.get('error')
                }

        except Exception as e:
            logger.error(f"❌ Error in trading cycle: {e}")
            return {'signals_generated': 0, 'trade_executed': False, 'error': str(e)}

    async def run_live_trading(self, duration_minutes: float = 30.0):
        """Run live trading session."""
        try:
            logger.info(f"🚀 Starting modern live trading session for {duration_minutes} minutes")

            await self.initialize()

            start_time = asyncio.get_event_loop().time()
            end_time = start_time + (duration_minutes * 60)

            cycle_count = 0
            successful_trades = 0
            total_signals = 0

            while asyncio.get_event_loop().time() < end_time:
                cycle_count += 1
                logger.info(f"🔄 Trading cycle {cycle_count}")

                # Run trading cycle
                result = await self.run_trading_cycle()

                total_signals += result.get('signals_generated', 0)
                if result.get('trade_executed'):
                    successful_trades += 1

                # Wait before next cycle
                await asyncio.sleep(60)  # 1 minute between cycles

            # Final summary
            logger.info(f"📊 Trading session completed:")
            logger.info(f"   Cycles: {cycle_count}")
            logger.info(f"   Total signals: {total_signals}")
            logger.info(f"   Successful trades: {successful_trades}")

            # Get executor metrics
            metrics = await self.modern_executor.get_metrics()
            logger.info(f"📈 Executor metrics: {metrics}")

            return True

        except Exception as e:
            logger.error(f"❌ Error in live trading session: {e}")
            return False

        finally:
            await self.close()

    async def close(self):
        """Clean up resources."""
        if self.modern_executor:
            await self.modern_executor.close()
        if self.jupiter_client:
            await self.jupiter_client.close()


async def main():
    """Main function for modern live trading."""
    import argparse

    parser = argparse.ArgumentParser(description="Modern Live Trading System")
    parser.add_argument("--duration", type=float, default=30.0, help="Trading duration in minutes")
    parser.add_argument("--dry-run", action="store_true", help="Run in dry-run mode")

    args = parser.parse_args()

    if args.dry_run:
        os.environ['DRY_RUN'] = 'true'

    print("🚀 MODERN LIVE TRADING SYSTEM")
    print("=" * 60)
    print("✅ Signature verification failures resolved")
    print("✅ Jito Bundle execution enabled")
    print("✅ Fresh blockhash handling implemented")
    print("=" * 60)

    trader = ModernLiveTrader()
    success = await trader.run_live_trading(args.duration)

    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
