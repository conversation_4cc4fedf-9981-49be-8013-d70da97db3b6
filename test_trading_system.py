#!/usr/bin/env python3
"""
Comprehensive test of the trading system.
"""

import sys
import os
import asyncio
import json
from datetime import datetime
from dotenv import load_dotenv

# Add current directory to path
sys.path.append('.')

# Load environment variables
load_dotenv()

async def test_signal_generation():
    """Test signal generation pipeline."""
    print("🔍 Testing Signal Generation")
    print("=" * 40)
    
    try:
        # Test Birdeye scanner
        from phase_4_deployment.data_router.birdeye_scanner import BirdeyeScanner
        
        scanner = BirdeyeScanner()
        print("✅ BirdeyeScanner created")
        
        # Scan for opportunities
        opportunities = await scanner.scan_for_opportunities(limit=3)
        print(f"✅ Found {len(opportunities)} opportunities")
        
        if opportunities:
            for i, opp in enumerate(opportunities):
                print(f"   {i+1}. {opp['symbol']}: ${opp['price']:.6f} (Score: {opp['score']:.2f})")
        
        await scanner.close()
        
        # Test signal enricher
        from phase_4_deployment.signal_generator.signal_enricher import SignalEnricher
        
        enricher = SignalEnricher()
        print("✅ SignalEnricher created")
        
        # Create test signals from opportunities
        signals = []
        for opp in opportunities[:2]:  # Take top 2
            signal = {
                'action': 'BUY',
                'market': f"{opp['symbol']}-USDC",
                'price': opp['price'],
                'size': 0.01,  # Small test size
                'confidence': opp['score'],
                'timestamp': datetime.now().isoformat(),
                'metadata': {
                    'momentum_score': opp['score'],
                    'liquidity_score': 0.7,
                    'volatility_score': 0.6,
                    'alpha_wallet_score': 0.5
                }
            }
            signals.append(signal)
        
        # Enrich signals
        enriched_signals = [enricher.enrich_signal(signal) for signal in signals]
        print(f"✅ Enriched {len(enriched_signals)} signals")
        
        # Sort by priority
        enriched_signals.sort(
            key=lambda s: s.get('metadata', {}).get('priority_score', 0),
            reverse=True
        )
        
        if enriched_signals:
            best_signal = enriched_signals[0]
            priority_score = best_signal.get('metadata', {}).get('priority_score', 0)
            print(f"🎯 Best signal: {best_signal['market']} (Priority: {priority_score:.3f})")
            return best_signal
        else:
            print("⚠️ No signals generated")
            return None
            
    except Exception as e:
        print(f"❌ Error in signal generation: {e}")
        return None

async def test_transaction_building():
    """Test transaction building."""
    print("\n🔍 Testing Transaction Building")
    print("=" * 40)
    
    try:
        # Create a test signal
        test_signal = {
            'action': 'BUY',
            'market': 'SOL-USDC',
            'price': 100.0,
            'size': 0.01,  # Very small test size
            'confidence': 0.8,
            'timestamp': datetime.now().isoformat()
        }
        
        # Test Orca swap builder
        from core.dex.orca_swap_builder import OrcaSwapBuilder
        
        wallet_address = os.getenv('WALLET_ADDRESS')
        orca_builder = OrcaSwapBuilder(wallet_address)
        await orca_builder.initialize()
        print("✅ Orca swap builder initialized")
        
        # Validate swap parameters (dry run)
        is_valid = await orca_builder.validate_swap_parameters(test_signal)
        print(f"✅ Swap parameters validation: {'PASS' if is_valid else 'FAIL'}")
        
        if is_valid:
            # Build transaction (dry run)
            print("🔨 Building test transaction...")
            result = await orca_builder.build_swap_transaction(test_signal)
            
            if result and result.get('success'):
                print("✅ Transaction built successfully")
                print(f"   Input: {result.get('input_amount', 0)} {result.get('input_token', 'Unknown')[:8]}...")
                print(f"   Output: {result.get('estimated_output', 0)} {result.get('output_token', 'Unknown')[:8]}...")
                return True
            else:
                error = result.get('error', 'Unknown error') if result else 'No result returned'
                print(f"❌ Transaction building failed: {error}")
                return False
        else:
            print("⚠️ Invalid swap parameters")
            return False
            
    except Exception as e:
        print(f"❌ Error in transaction building: {e}")
        return False

async def test_unified_trader():
    """Test the unified trader initialization."""
    print("\n🔍 Testing Unified Trader")
    print("=" * 40)
    
    try:
        from scripts.unified_live_trading import UnifiedLiveTrader
        
        # Initialize trader
        trader = UnifiedLiveTrader()
        print("✅ UnifiedLiveTrader created")
        
        # Check validation
        if trader.validation_errors:
            print("⚠️ Validation errors found:")
            for error in trader.validation_errors:
                print(f"   - {error}")
            return False
        else:
            print("✅ Environment validation passed")
        
        # Initialize components
        print("🔧 Initializing trading components...")
        success = await trader.initialize_components()
        
        if success:
            print("✅ All components initialized successfully")
            
            # Test wallet balance check
            balance_ok = await trader.check_wallet_balance()
            print(f"✅ Wallet balance check: {'PASS' if balance_ok else 'FAIL'}")
            
            return balance_ok
        else:
            print("❌ Component initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing unified trader: {e}")
        return False

async def test_system_status():
    """Test system status check."""
    print("\n🔍 Testing System Status")
    print("=" * 40)
    
    try:
        # Run system status check
        import subprocess
        result = subprocess.run([
            '/usr/bin/python3', 'scripts/system_status_check.py'
        ], capture_output=True, text=True, cwd='/Users/<USER>/HedgeFund')
        
        if result.returncode == 0:
            print("✅ System status check completed")
            # Extract key metrics from output
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if 'SYSTEM STATUS:' in line:
                    print(f"   {line.strip()}")
                elif 'CPU:' in line or 'Memory:' in line:
                    print(f"   {line.strip()}")
            return True
        else:
            print(f"❌ System status check failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking system status: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 SYNERGY7 COMPREHENSIVE TRADING SYSTEM TEST")
    print("=" * 60)
    
    # Test signal generation
    signal = await test_signal_generation()
    signal_success = signal is not None
    
    # Test transaction building
    tx_success = await test_transaction_building()
    
    # Test unified trader
    trader_success = await test_unified_trader()
    
    # Test system status
    status_success = await test_system_status()
    
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST RESULTS:")
    print(f"   Signal Generation: {'✅ PASS' if signal_success else '❌ FAIL'}")
    print(f"   Transaction Building: {'✅ PASS' if tx_success else '❌ FAIL'}")
    print(f"   Unified Trader: {'✅ PASS' if trader_success else '❌ FAIL'}")
    print(f"   System Status: {'✅ PASS' if status_success else '❌ FAIL'}")
    
    all_success = signal_success and tx_success and trader_success and status_success
    
    if all_success:
        print("\n🎉 ALL TESTS PASSED! System is 100% functional and ready for live trading!")
        print("\n🚀 Ready to execute trades with:")
        print("   python scripts/unified_live_trading.py --duration 10")
    else:
        print("\n⚠️ Some tests failed. Review the issues above before live trading.")
    
    return all_success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
