#!/bin/bash
# Activation script for venv_new environment
# Usage: source activate_venv.sh

echo "🔧 Activating venv_new environment..."

# Check if we're in the correct directory
if [ ! -d "venv_new" ]; then
    echo "❌ Error: venv_new directory not found. Please run this from the HedgeFund directory."
    return 1
fi

# Activate the virtual environment
source venv_new/bin/activate

# Verify activation
if [ "$VIRTUAL_ENV" = "$(pwd)/venv_new" ]; then
    echo "✅ venv_new activated successfully!"
    echo "📍 Virtual environment: $VIRTUAL_ENV"
    echo "🐍 Python location: $(which python)"
    echo ""
    echo "📋 Available commands:"
    echo "   python scripts/test_signature_fix.py          # Test signature verification fix"
    echo "   python scripts/modern_live_trading.py --help  # Modern trading system"
    echo "   python scripts/quick_component_test.py        # Quick component test"
    echo ""
    echo "💡 To deactivate: deactivate"
else
    echo "❌ Error: Failed to activate venv_new"
    return 1
fi
