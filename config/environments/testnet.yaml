# Synergy7 Testnet Environment Configuration
# Full end-to-end testing with real transaction execution on testnet

# System mode
mode:
  live_trading: true  # Enable live trading for real transaction testing
  paper_trading: false  # Disable paper trading - we want real testnet transactions
  backtesting: false
  simulation: false

# Solana testnet configuration
solana:
  rpc_url: "https://api.testnet.solana.com"
  private_rpc_url: "https://api.testnet.solana.com"
  fallback_rpc_url: "https://api.testnet.solana.com"
  commitment: "confirmed"
  max_retries: 5
  retry_delay: 2.0  # Longer delay for testnet
  tx_timeout: 60    # Longer timeout for testnet
  provider: "solana"

# Wallet configuration for testnet
wallet:
  address: ${TESTNET_WALLET_ADDRESS}  # Use separate testnet wallet
  keypair_path: "wallet/testnet_keypair.json"  # Separate testnet keypair
  state_sync_interval: 30  # More frequent sync for testing
  position_update_interval: 60
  max_positions: 5  # Fewer positions for testing
  data_dir: "data/testnet"

# Risk management (conservative for testing)
risk:
  max_position_size_usd: 10   # Very small USD amounts
  max_position_size_pct: 0.05 # 5% of portfolio max
  stop_loss_pct: 0.03         # 3% stop loss
  take_profit_pct: 0.06       # 6% take profit
  max_drawdown_pct: 0.10      # 10% max drawdown
  daily_loss_limit_usd: 5     # $5 daily loss limit
  circuit_breaker_enabled: true
  position_sizing_enabled: true
  var_calculation_enabled: true
  correlation_analysis_enabled: true

# Execution configuration for testnet
execution:
  use_jito: false  # Jito not available on testnet
  slippage_tolerance: 0.05  # Higher slippage tolerance for testnet
  max_spread_pct: 0.10  # 10% max spread for testnet
  max_retries: 5
  retry_delay: 3.0
  timeout: 60
  priority_fee: 1000  # Minimal priority fee for testnet
  compute_unit_limit: 200000
  compute_unit_price: 1

# API integrations (testnet compatible)
apis:
  helius:
    enabled: true
    api_key: ${HELIUS_API_KEY}
    endpoint: "https://api.helius.dev/v0"
    rpc_endpoint: "https://api.testnet.solana.com"  # Use public testnet RPC
    ws_endpoint: "wss://api.testnet.solana.com"
    use_enhanced_apis: false  # Disable enhanced APIs for testnet

  birdeye:
    enabled: false  # Birdeye doesn't support testnet
    api_key: ${BIRDEYE_API_KEY}
    endpoint: "https://api.birdeye.so/v1"

  jito:
    enabled: false  # Jito not available on testnet
    rpc_url: "https://api.testnet.solana.com"
    shredstream_url: ""
    keypair_path: ""

# Trading configuration
trading:
  enabled: true
  dry_run: false  # Real transactions on testnet
  max_trades_per_hour: 10
  min_trade_interval: 60  # 1 minute between trades
  max_concurrent_trades: 2
  trade_timeout: 300  # 5 minutes

# Signal generation (simplified for testnet)
signal_generation:
  enabled: true
  whale_watching:
    enabled: false  # Disable whale watching (no real whales on testnet)
    min_transaction_size: 1000
    tracking_duration: 3600

  market_regime:
    enabled: false  # Disable market regime detection
    lookback_period: 100
    volatility_threshold: 0.02

  technical_analysis:
    enabled: true
    indicators: ["sma", "rsi", "macd"]
    timeframes: ["1m", "5m", "15m"]

# Monitoring and alerts
monitoring:
  enabled: true
  health_check_interval: 30
  metrics_collection_interval: 60
  alert_thresholds:
    error_rate: 0.1
    latency_ms: 5000
    memory_usage_pct: 80

# Telegram notifications (testnet specific)
telegram:
  enabled: true
  bot_token: ${TELEGRAM_BOT_TOKEN}
  chat_id: ${TELEGRAM_CHAT_ID}
  rate_limit_seconds: 60  # More frequent notifications for testing
  alerts:
    trade_execution: true
    system_errors: true
    balance_changes: true
    performance_updates: true

# Deployment settings
deployment:
  environment: "testnet"
  docker:
    image: "synergy7:testnet"
    container_name: "synergy7_testnet"
    restart_policy: "no"

  streamlit:
    port: 8502  # Different port for testnet dashboard
    headless: false
    theme:
      base: "dark"
      primary_color: "#FFA500"  # Orange theme for testnet
      background_color: "#0E1117"
      secondary_background_color: "#262730"

# Logging configuration
logging:
  level: "DEBUG"  # Verbose logging for testing
  file_path: "logs/testnet_trading.log"
  max_file_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Data sources (testnet compatible)
data_sources:
  price_feeds:
    - name: "solana_testnet"
      endpoint: "https://api.testnet.solana.com"
      enabled: true

  market_data:
    - name: "mock_data"
      enabled: true
      update_interval: 60

# Strategy configuration (simplified for testnet)
strategies:
  - name: "testnet_momentum"
    enabled: true
    params:
      lookback_period: 20
      momentum_threshold: 0.02
      position_size: 0.01  # Very small positions

  - name: "testnet_mean_reversion"
    enabled: true
    params:
      lookback_period: 50
      deviation_threshold: 2.0
      position_size: 0.01

# Testnet specific settings
testnet:
  faucet_url: "https://faucet.solana.com"
  explorer_url: "https://explorer.solana.com/?cluster=testnet"
  auto_request_sol: true  # Automatically request SOL from faucet if balance low
  min_sol_balance: 1.0    # Minimum SOL balance to maintain
  max_sol_request: 2.0    # Maximum SOL to request from faucet

# Token registry for testnet
tokens:
  SOL: "So11111111111111111111111111111111111111112"
  USDC: "CpMah17kQEL2wqyMKt3mZBdTnZbkbfx4nqmQMFDP5vwp"  # Testnet USDC
  USDT: "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"  # Testnet USDT

# DEX configuration (testnet compatible)
dex:
  orca:
    enabled: true
    program_id: "whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc"  # Same on testnet
    pools:
      SOL_USDC: "testnet_pool_address_here"  # Would need actual testnet pool addresses

  jupiter:
    enabled: false  # Jupiter may not be fully available on testnet

# Performance testing
performance:
  max_test_duration: 3600  # 1 hour max test duration
  target_trades: 10        # Target number of trades for testing
  success_rate_threshold: 0.8  # 80% success rate required
  latency_threshold_ms: 2000   # 2 second max latency
