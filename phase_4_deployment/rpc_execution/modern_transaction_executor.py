#!/usr/bin/env python3
"""
Modern Transaction Executor with Jito Bundles and Advanced Error Handling
Resolves signature verification failures through modern Solana transaction practices.
"""

import asyncio
import logging
import time
import base64
import base58
from typing import Dict, Any, Optional, List
import httpx
from solders.transaction import VersionedTransaction
from solders.keypair import Keypair
from solders.hash import Hash

logger = logging.getLogger(__name__)

class ModernTransactionExecutor:
    """Modern transaction executor with Jito Bundles and premium RPC handling."""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # RPC Configuration
        self.primary_rpc = self.config.get('primary_rpc', 'https://mainnet.helius-rpc.com')
        self.fallback_rpc = self.config.get('fallback_rpc', 'https://api.mainnet-beta.solana.com')
        self.jito_rpc = self.config.get('jito_rpc', 'https://mainnet.block-engine.jito.wtf/api/v1')
        
        # API Keys
        self.helius_api_key = self.config.get('helius_api_key')
        self.quicknode_api_key = self.config.get('quicknode_api_key')
        
        # HTTP Clients
        self.primary_client = None
        self.fallback_client = None
        self.jito_client = None
        
        # Circuit Breaker State
        self.circuit_breaker = {
            'primary': {'failures': 0, 'last_failure': 0, 'state': 'CLOSED'},
            'fallback': {'failures': 0, 'last_failure': 0, 'state': 'CLOSED'},
            'jito': {'failures': 0, 'last_failure': 0, 'state': 'CLOSED'}
        }
        
        # Metrics
        self.metrics = {
            'total_transactions': 0,
            'successful_transactions': 0,
            'failed_transactions': 0,
            'signature_verification_failures': 0,
            'jito_bundle_successes': 0,
            'average_execution_time': 0.0
        }
        
    async def initialize(self):
        """Initialize HTTP clients with proper configuration."""
        try:
            # Primary RPC client (Helius with API key)
            primary_url = f"{self.primary_rpc}/?api-key={self.helius_api_key}" if self.helius_api_key else self.primary_rpc
            self.primary_client = httpx.AsyncClient(
                timeout=30.0,
                limits=httpx.Limits(max_connections=20, max_keepalive_connections=10)
            )
            
            # Fallback RPC client
            self.fallback_client = httpx.AsyncClient(
                timeout=30.0,
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
            
            # Jito client for bundles
            self.jito_client = httpx.AsyncClient(
                timeout=30.0,
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
            
            logger.info("✅ Modern transaction executor initialized")
            
        except Exception as e:
            logger.error(f"❌ Error initializing transaction executor: {e}")
            raise

    async def execute_transaction_with_bundles(self, 
                                             transaction: str, 
                                             opts: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute transaction using Jito Bundles for atomic execution and MEV protection."""
        start_time = time.time()
        self.metrics['total_transactions'] += 1
        
        try:
            # Decode transaction
            if isinstance(transaction, str):
                try:
                    # Try base64 first
                    tx_bytes = base64.b64decode(transaction)
                except:
                    # Fallback to base58
                    tx_bytes = base58.b58decode(transaction)
            else:
                tx_bytes = transaction
            
            # Parse transaction
            tx = VersionedTransaction.from_bytes(tx_bytes)
            
            # Get fresh blockhash and update transaction
            fresh_blockhash = await self._get_fresh_blockhash()
            if fresh_blockhash:
                tx.message.recent_blockhash = Hash.from_string(fresh_blockhash)
                logger.info(f"✅ Updated transaction with fresh blockhash: {fresh_blockhash}")
            
            # Serialize updated transaction
            updated_tx_bytes = bytes(tx)
            
            # Try Jito Bundle execution first
            if self._is_circuit_open('jito'):
                logger.warning("⚠️ Jito circuit breaker is open, using fallback")
            else:
                bundle_result = await self._execute_jito_bundle([updated_tx_bytes])
                if bundle_result.get('success'):
                    execution_time = time.time() - start_time
                    self._update_metrics(True, execution_time)
                    self.metrics['jito_bundle_successes'] += 1
                    return bundle_result
                else:
                    self._record_failure('jito')
            
            # Fallback to regular transaction execution
            return await self._execute_regular_transaction(updated_tx_bytes, opts)
            
        except Exception as e:
            logger.error(f"❌ Error executing transaction: {e}")
            execution_time = time.time() - start_time
            self._update_metrics(False, execution_time)
            
            return {
                'success': False,
                'error': f"Transaction execution failed: {str(e)}",
                'provider': None,
                'signature': None
            }

    async def _execute_jito_bundle(self, transactions: List[bytes]) -> Dict[str, Any]:
        """Execute transactions as a Jito Bundle."""
        try:
            if not self.jito_client:
                await self.initialize()
            
            # Encode transactions for bundle
            encoded_txs = [base64.b64encode(tx).decode('utf-8') for tx in transactions]
            
            # Prepare bundle request
            bundle_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "sendBundle",
                "params": [encoded_txs]
            }
            
            # Send bundle to Jito
            response = await self.jito_client.post(
                f"{self.jito_rpc}/bundles",
                json=bundle_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                if "result" in result:
                    bundle_id = result["result"]
                    logger.info(f"✅ Jito bundle submitted successfully: {bundle_id}")
                    
                    # Wait for bundle confirmation
                    confirmation = await self._wait_for_bundle_confirmation(bundle_id)
                    
                    return {
                        'success': True,
                        'bundle_id': bundle_id,
                        'confirmation': confirmation,
                        'provider': 'jito_bundle',
                        'signature': confirmation.get('signature') if confirmation else None
                    }
                else:
                    error_msg = result.get('error', {}).get('message', 'Unknown error')
                    logger.error(f"❌ Jito bundle failed: {error_msg}")
                    return {'success': False, 'error': error_msg}
            else:
                logger.error(f"❌ Jito bundle HTTP error: {response.status_code}")
                return {'success': False, 'error': f"HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"❌ Error executing Jito bundle: {e}")
            return {'success': False, 'error': str(e)}

    async def _execute_regular_transaction(self, tx_bytes: bytes, opts: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute transaction using regular RPC with retry logic."""
        try:
            # Encode transaction
            encoded_tx = base64.b64encode(tx_bytes).decode('utf-8')
            
            # Prepare transaction request
            tx_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "sendTransaction",
                "params": [
                    encoded_tx,
                    {
                        "encoding": "base64",
                        "skipPreflight": opts.get('skip_preflight', False) if opts else False,
                        "preflightCommitment": "confirmed",
                        "maxRetries": 0  # We handle retries ourselves
                    }
                ]
            }
            
            # Try primary RPC first
            if not self._is_circuit_open('primary'):
                result = await self._send_transaction_request(self.primary_client, self.primary_rpc, tx_request)
                if result.get('success'):
                    return result
                else:
                    self._record_failure('primary')
                    if 'signature verification failure' in str(result.get('error', '')).lower():
                        self.metrics['signature_verification_failures'] += 1
            
            # Fallback to secondary RPC
            if not self._is_circuit_open('fallback'):
                result = await self._send_transaction_request(self.fallback_client, self.fallback_rpc, tx_request)
                if result.get('success'):
                    return result
                else:
                    self._record_failure('fallback')
            
            return {'success': False, 'error': 'All RPC endpoints failed'}
            
        except Exception as e:
            logger.error(f"❌ Error in regular transaction execution: {e}")
            return {'success': False, 'error': str(e)}

    async def _send_transaction_request(self, client: httpx.AsyncClient, rpc_url: str, request: Dict[str, Any]) -> Dict[str, Any]:
        """Send transaction request to RPC endpoint."""
        try:
            response = await client.post(rpc_url, json=request)
            response.raise_for_status()
            
            result = response.json()
            
            if "result" in result:
                signature = result["result"]
                logger.info(f"✅ Transaction sent successfully: {signature}")
                return {
                    'success': True,
                    'signature': signature,
                    'provider': rpc_url
                }
            elif "error" in result:
                error_msg = result["error"].get("message", "Unknown error")
                logger.error(f"❌ RPC error: {error_msg}")
                return {'success': False, 'error': error_msg}
            else:
                return {'success': False, 'error': 'Invalid RPC response'}
                
        except Exception as e:
            logger.error(f"❌ Error sending transaction request: {e}")
            return {'success': False, 'error': str(e)}

    async def _get_fresh_blockhash(self) -> Optional[str]:
        """Get fresh blockhash from the most reliable RPC."""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getLatestBlockhash",
                "params": [{"commitment": "finalized"}]
            }
            
            # Try primary RPC first
            if not self._is_circuit_open('primary'):
                try:
                    response = await self.primary_client.post(self.primary_rpc, json=request)
                    response.raise_for_status()
                    result = response.json()
                    
                    if "result" in result and "value" in result["result"]:
                        return result["result"]["value"]["blockhash"]
                except:
                    pass
            
            # Fallback RPC
            if not self._is_circuit_open('fallback'):
                try:
                    response = await self.fallback_client.post(self.fallback_rpc, json=request)
                    response.raise_for_status()
                    result = response.json()
                    
                    if "result" in result and "value" in result["result"]:
                        return result["result"]["value"]["blockhash"]
                except:
                    pass
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting fresh blockhash: {e}")
            return None

    async def _wait_for_bundle_confirmation(self, bundle_id: str, timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """Wait for Jito bundle confirmation."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check bundle status
                status_request = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getBundleStatuses",
                    "params": [[bundle_id]]
                }
                
                response = await self.jito_client.post(
                    f"{self.jito_rpc}/bundles",
                    json=status_request
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if "result" in result and result["result"]:
                        status = result["result"][0]
                        if status.get("confirmation_status") == "confirmed":
                            return status
                
                await asyncio.sleep(1.0)  # Wait 1 second before retry
                
            except Exception as e:
                logger.warning(f"⚠️ Error checking bundle status: {e}")
                await asyncio.sleep(1.0)
        
        logger.warning(f"⚠️ Bundle confirmation timeout: {bundle_id}")
        return None

    def _is_circuit_open(self, provider: str) -> bool:
        """Check if circuit breaker is open for a provider."""
        circuit = self.circuit_breaker.get(provider, {})
        if circuit.get('state') == 'OPEN':
            # Check if reset timeout has passed
            if time.time() - circuit.get('last_failure', 0) > 60:  # 60 second reset
                circuit['state'] = 'CLOSED'
                circuit['failures'] = 0
                return False
            return True
        return False

    def _record_failure(self, provider: str):
        """Record failure for circuit breaker."""
        circuit = self.circuit_breaker.get(provider, {})
        circuit['failures'] = circuit.get('failures', 0) + 1
        circuit['last_failure'] = time.time()
        
        if circuit['failures'] >= 3:  # Open circuit after 3 failures
            circuit['state'] = 'OPEN'
            logger.warning(f"⚠️ Circuit breaker opened for {provider}")

    def _update_metrics(self, success: bool, execution_time: float):
        """Update execution metrics."""
        if success:
            self.metrics['successful_transactions'] += 1
        else:
            self.metrics['failed_transactions'] += 1
        
        # Update average execution time
        total_txs = self.metrics['successful_transactions'] + self.metrics['failed_transactions']
        current_avg = self.metrics['average_execution_time']
        self.metrics['average_execution_time'] = ((current_avg * (total_txs - 1)) + execution_time) / total_txs

    async def get_metrics(self) -> Dict[str, Any]:
        """Get executor metrics."""
        return {
            **self.metrics,
            'circuit_breaker_status': self.circuit_breaker
        }

    async def close(self):
        """Close all HTTP clients."""
        if self.primary_client:
            await self.primary_client.aclose()
        if self.fallback_client:
            await self.fallback_client.aclose()
        if self.jito_client:
            await self.jito_client.aclose()
        
        logger.info("✅ Modern transaction executor closed")
