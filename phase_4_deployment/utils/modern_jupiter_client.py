#!/usr/bin/env python3
"""
Modern Jupiter Client with Fresh Blockhash Handling
Resolves signature verification failures through optimized transaction building.
"""

import httpx
import logging
import asyncio
import base64
import time
from typing import Optional, Dict, Any
from solders.transaction import VersionedTransaction
from solders.keypair import Keypair
from solders.hash import Hash

logger = logging.getLogger(__name__)

class ModernJupiterClient:
    """Modern Jupiter client with fresh blockhash handling and optimized execution."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize with unified configuration."""
        # Load unified configuration if not provided
        if config is None:
            from phase_4_deployment.core.unified_config import get_unified_config
            unified_config = get_unified_config()
            unified_config.load()

            self.jupiter_config = unified_config.get_jupiter_config()
            self.rpc_config = unified_config.get_rpc_config()
        else:
            # Use provided config (for backward compatibility)
            self.jupiter_config = {
                'api_url': config.get('jupiter_api_url', 'https://quote-api.jup.ag/v6'),
                'default_slippage_bps': config.get('slippage_bps', 50),
                'timeout_seconds': config.get('timeout', 5.0),
                'max_accounts': config.get('max_accounts', 20),
                'auto_slippage': config.get('auto_slippage', True)
            }
            self.rpc_config = {
                'primary_rpc': config.get('rpc_url', 'https://api.mainnet-beta.solana.com'),
                'timeout': config.get('timeout', 30.0)
            }

        self.base_url = self.jupiter_config['api_url']
        self.rpc_url = self.rpc_config['primary_rpc']
        self.timeout = self.jupiter_config['timeout_seconds']
        self.client = None
        self.rpc_client = None

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

    async def _get_fresh_blockhash(self) -> Optional[str]:
        """Get fresh blockhash from RPC."""
        try:
            if not self.rpc_client:
                self.rpc_client = httpx.AsyncClient(timeout=10.0)

            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getLatestBlockhash",
                "params": [{"commitment": "finalized"}]
            }

            response = await self.rpc_client.post(self.rpc_url, json=payload)
            response.raise_for_status()

            result = response.json()
            if "result" in result and "value" in result["result"]:
                blockhash = result["result"]["value"]["blockhash"]
                logger.debug(f"Fresh blockhash obtained: {blockhash}")
                return blockhash

            return None
        except Exception as e:
            logger.error(f"Error getting fresh blockhash: {e}")
            return None

    async def get_optimized_quote(self,
                                input_mint: str,
                                output_mint: str,
                                amount: int,
                                slippage_bps: int = None) -> Optional[Dict[str, Any]]:
        """Get optimized quote with minimal latency."""
        try:
            if not self.client:
                self.client = httpx.AsyncClient(timeout=self.timeout)

            # Use configuration values instead of hardcoded ones
            if slippage_bps is None:
                slippage_bps = self.jupiter_config['default_slippage_bps']

            params = {
                "inputMint": input_mint,
                "outputMint": output_mint,
                "amount": amount,
                "slippageBps": slippage_bps,
                "onlyDirectRoutes": "true",  # Faster routing
                "asLegacyTransaction": "false",  # Use versioned transactions
                "maxAccounts": self.jupiter_config['max_accounts'],
                "autoSlippage": str(self.jupiter_config['auto_slippage']).lower(),
                "autoSlippageCollisionUsdValue": 1000
            }

            start_time = time.time()
            response = await self.client.get(f"{self.base_url}/quote", params=params)
            response.raise_for_status()

            quote_time = time.time() - start_time
            quote_data = response.json()

            logger.info(f"✅ Optimized Jupiter quote received in {quote_time:.2f}s: {quote_data.get('outAmount', 'unknown')} output")
            return quote_data

        except Exception as e:
            logger.error(f"❌ Error getting optimized Jupiter quote: {e}")
            return None

    async def build_optimized_transaction(self,
                                        quote: Dict[str, Any],
                                        user_public_key: str,
                                        priority_fee_lamports: int = None) -> Optional[bytes]:
        """Build optimized transaction with fresh blockhash."""
        try:
            if not self.client:
                self.client = httpx.AsyncClient(timeout=5.0)

            # Calculate dynamic priority fee if not provided
            if priority_fee_lamports is None:
                priority_fee_lamports = await self._calculate_priority_fee()

            swap_request = {
                "quoteResponse": quote,
                "userPublicKey": user_public_key,
                "wrapAndUnwrapSol": True,
                "useSharedAccounts": True,
                "feeAccount": None,
                "computeUnitPriceMicroLamports": priority_fee_lamports,
                "asLegacyTransaction": False,  # Use versioned transactions
                "dynamicComputeUnitLimit": True,
                "prioritizationFeeLamports": "auto"
            }

            start_time = time.time()
            response = await self.client.post(
                f"{self.base_url}/swap",
                json=swap_request,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()

            build_time = time.time() - start_time
            swap_data = response.json()

            if "swapTransaction" in swap_data:
                transaction_data = swap_data["swapTransaction"]

                # Decode and update with fresh blockhash
                tx_bytes = base64.b64decode(transaction_data)
                tx = VersionedTransaction.from_bytes(tx_bytes)

                # Get fresh blockhash and update transaction
                fresh_blockhash = await self._get_fresh_blockhash()
                if fresh_blockhash:
                    # Update transaction with fresh blockhash
                    tx.message.recent_blockhash = Hash.from_string(fresh_blockhash)
                    logger.info(f"✅ Transaction updated with fresh blockhash: {fresh_blockhash}")

                # Return updated transaction bytes
                updated_tx_bytes = bytes(tx)
                logger.info(f"✅ Optimized transaction built in {build_time:.2f}s: {len(updated_tx_bytes)} bytes")
                return updated_tx_bytes
            else:
                logger.error("❌ No swap transaction in Jupiter response")
                return None

        except Exception as e:
            logger.error(f"❌ Error building optimized transaction: {e}")
            return None

    async def _calculate_priority_fee(self) -> int:
        """Calculate dynamic priority fee based on network conditions."""
        try:
            if not self.rpc_client:
                self.rpc_client = httpx.AsyncClient(timeout=5.0)

            # Get recent priority fees
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getRecentPrioritizationFees",
                "params": [{"lockedWritableAccounts": []}]
            }

            response = await self.rpc_client.post(self.rpc_url, json=payload)
            response.raise_for_status()

            result = response.json()
            if "result" in result and result["result"]:
                fees = [fee["prioritizationFee"] for fee in result["result"]]
                if fees:
                    # Use 75th percentile for competitive priority
                    fees.sort()
                    percentile_75 = fees[int(len(fees) * 0.75)]
                    priority_fee = max(percentile_75, 1000)  # Minimum 1000 micro-lamports
                    logger.debug(f"Calculated priority fee: {priority_fee} micro-lamports")
                    return priority_fee

            # Fallback to default
            return 5000  # 5000 micro-lamports default

        except Exception as e:
            logger.warning(f"Error calculating priority fee, using default: {e}")
            return 5000

    async def sign_and_prepare_transaction(self, tx_bytes: bytes, keypair: Keypair) -> Optional[bytes]:
        """Sign transaction with fresh blockhash and prepare for submission."""
        try:
            # Decode transaction
            tx = VersionedTransaction.from_bytes(tx_bytes)

            # Get fresh blockhash immediately before signing
            fresh_blockhash = await self._get_fresh_blockhash()
            if fresh_blockhash:
                tx.message.recent_blockhash = Hash.from_string(fresh_blockhash)
                logger.info(f"✅ Updated transaction with fresh blockhash: {fresh_blockhash}")

            # Sign transaction
            tx.sign([keypair])

            # Verify signature
            if not tx.signatures or not tx.signatures[0]:
                logger.error("❌ Transaction signing failed - no signature generated")
                return None

            # Return signed transaction bytes
            signed_tx_bytes = bytes(tx)
            logger.info(f"✅ Transaction signed successfully: {len(signed_tx_bytes)} bytes")
            return signed_tx_bytes

        except Exception as e:
            logger.error(f"❌ Error signing transaction: {e}")
            return None

    async def close(self):
        """Close HTTP clients."""
        if self.client:
            await self.client.aclose()
            self.client = None
        if self.rpc_client:
            await self.rpc_client.aclose()
            self.rpc_client = None


class OptimizedTransactionBuilder:
    """Optimized transaction builder with modern Solana practices."""

    def __init__(self, rpc_url: str = None, keypair: Keypair = None):
        self.jupiter_client = ModernJupiterClient(rpc_url)
        self.keypair = keypair

    async def build_swap_transaction(self, signal: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Build optimized swap transaction from signal."""
        try:
            # Extract signal data
            market = signal.get('market', 'SOL/USDC')
            action = signal.get('action', 'buy')
            amount = signal.get('amount', 0.01)  # SOL amount

            # Token mapping
            token_mapping = {
                'SOL/USDC': {
                    'input': 'So11111111111111111111111111111111111111112',  # SOL
                    'output': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'  # USDC
                }
            }

            if market not in token_mapping:
                logger.error(f"❌ Unsupported market: {market}")
                return None

            tokens = token_mapping[market]

            if action == 'buy':
                # Buy SOL with USDC
                input_mint = tokens['output']  # USDC
                output_mint = tokens['input']  # SOL
                # Convert USDC amount (assuming we want to spend $1 worth of USDC)
                amount_lamports = int(1 * 1_000_000)  # $1 USDC in micro-USDC
            else:
                # Sell SOL for USDC
                input_mint = tokens['input']  # SOL
                output_mint = tokens['output']  # USDC
                # Convert SOL amount to lamports
                amount_lamports = int(amount * 1_000_000_000)  # SOL to lamports

            # Get optimized quote
            quote = await self.jupiter_client.get_optimized_quote(
                input_mint, output_mint, amount_lamports, slippage_bps=50
            )

            if not quote:
                return None

            # Build optimized transaction
            tx_bytes = await self.jupiter_client.build_optimized_transaction(
                quote, str(self.keypair.pubkey())
            )

            if not tx_bytes:
                return None

            # Sign and prepare transaction
            signed_tx_bytes = await self.jupiter_client.sign_and_prepare_transaction(
                tx_bytes, self.keypair
            )

            if not signed_tx_bytes:
                return None

            return {
                'success': True,
                'transaction': base64.b64encode(signed_tx_bytes).decode('utf-8'),
                'quote': quote,
                'market': market,
                'action': action,
                'amount': amount
            }

        except Exception as e:
            logger.error(f"❌ Error building swap transaction: {e}")
            return None

    async def close(self):
        """Close Jupiter client."""
        await self.jupiter_client.close()
