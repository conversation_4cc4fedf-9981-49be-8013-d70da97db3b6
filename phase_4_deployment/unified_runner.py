#!/usr/bin/env python3
"""
Unified Runner for Synergy7 Trading System

This script provides the primary unified entry point for the Synergy7 Trading System,
supporting different operational modes (live, paper, backtest, simulation).

This is the RECOMMENDED entry point for all production deployments.
"""

import os
import sys
import time
import json
import logging
import asyncio
import argparse
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("unified_runner")

# Make logger available globally for imports
# This is a safer way to make the logger available
import builtins
setattr(builtins, 'logger', logger)

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import configuration modules
try:
    from phase_4_deployment.core.config_loader import load_config
except ImportError:
    try:
        from utils.config.config_loader import load_config
    except ImportError:
        # Fallback to simple YAML loading
        import yaml
        def load_config(config_path: str = "config.yaml") -> dict:
            """Simple config loader fallback."""
            try:
                with open(config_path, 'r') as f:
                    return yaml.safe_load(f) or {}
            except Exception as e:
                logger.error(f"Error loading config: {e}")
                return {}

# Modern transaction system imports for signature verification fix
try:
    from rpc_execution.modern_transaction_executor import ModernTransactionExecutor
    from utils.modern_jupiter_client import OptimizedTransactionBuilder
    MODERN_TRANSACTION_AVAILABLE = True
    logger.info("✅ Modern transaction system available - signature verification fix enabled")
except ImportError as e:
    MODERN_TRANSACTION_AVAILABLE = False
    logger.warning(f"⚠️ Modern transaction system not available: {e}")
    logger.warning("⚠️ Signature verification failures may occur - consider installing modern components")

# Import monitoring modules
class SimpleMonitoringService:
    """Simple monitoring service for unified runner."""

    def __init__(self):
        self.components = {}
        self.alert_handlers = {}
        self.running = False

    def register_component(self, name, health_check_func):
        """Register a component for health checking."""
        self.components[name] = health_check_func

    def register_alert_handler(self, alert_type, handler):
        """Register an alert handler."""
        self.alert_handlers[alert_type] = handler

    def start_health_checks(self):
        """Start health checks."""
        self.running = True
        logger.info("Health checks started")

    def stop_health_checks(self):
        """Stop health checks."""
        self.running = False
        logger.info("Health checks stopped")

def get_monitoring_service():
    """Get monitoring service instance."""
    return SimpleMonitoringService()

def setup_telegram_alerts(bot_token, chat_id, rate_limit_seconds=300):
    """Setup telegram alerts."""
    logger.info("Telegram alerts configured")
    return lambda message: logger.info(f"Alert: {message}")

def start_health_server(host="0.0.0.0", port=8080):
    """Simple health server placeholder."""
    logger.info(f"Health server started on {host}:{port}")
    return True

class UnifiedRunner:
    """Unified runner for Synergy7 Trading System."""

    def __init__(self, mode: str, config_path: str = "config.yaml", env_file: str = ".env"):
        """
        Initialize the unified runner.

        Args:
            mode: Operational mode (live, paper, backtest, simulation)
            config_path: Path to the configuration file
            env_file: Path to the environment file
        """
        self.mode = mode
        self.config_path = config_path

        # Set environment file based on mode if not explicitly provided
        if env_file == ".env":
            if mode == "paper":
                self.env_file = ".env.paper"
                logger.info(f"Using paper trading environment file: {self.env_file}")
            elif mode == "simulation":
                self.env_file = ".env.simulation"
                logger.info(f"Using simulation environment file: {self.env_file}")
            else:
                self.env_file = env_file
        else:
            self.env_file = env_file

        # Load environment variables from the environment file
        self._load_env_file()

        self.config = None
        self.monitoring = None
        self.health_server = None
        self.streamlit_process = None

        # Initialize component references
        self.filter_chain = None
        self.signal_enricher = None
        self.rl_data_collector = None

        # Set environment variables based on mode
        self._set_environment_variables()

    def _load_env_file(self):
        """Load environment variables from the environment file."""
        try:
            # Check if the environment file exists
            if not os.path.exists(self.env_file):
                logger.warning(f"Environment file {self.env_file} not found, using default environment variables")
                return

            # Load environment variables from the file
            logger.info(f"Loading environment variables from {self.env_file}")
            from dotenv import load_dotenv
            load_dotenv(self.env_file, override=True)

            # Log loaded environment variables (excluding sensitive ones)
            safe_vars = ['DRY_RUN', 'PAPER_TRADING', 'TRADING_ENABLED', 'SIMULATION_MODE', 'LOG_LEVEL']
            for var in safe_vars:
                if var in os.environ:
                    logger.info(f"Loaded {var}={os.environ[var]}")

            logger.info(f"Environment variables loaded from {self.env_file}")
        except Exception as e:
            logger.error(f"Error loading environment variables from {self.env_file}: {str(e)}")

    def _set_environment_variables(self):
        """Set environment variables based on the operational mode."""
        if self.mode == "live":
            os.environ["TRADING_ENABLED"] = "true"
            os.environ["PAPER_TRADING"] = "false"
            os.environ["BACKTESTING_ENABLED"] = "false"
            os.environ["DRY_RUN"] = "false"
        elif self.mode == "paper":
            os.environ["TRADING_ENABLED"] = "true"
            os.environ["PAPER_TRADING"] = "true"
            os.environ["BACKTESTING_ENABLED"] = "false"
            os.environ["DRY_RUN"] = "true"
        elif self.mode == "backtest":
            os.environ["TRADING_ENABLED"] = "false"
            os.environ["PAPER_TRADING"] = "false"
            os.environ["BACKTESTING_ENABLED"] = "true"
            os.environ["DRY_RUN"] = "true"
        elif self.mode == "simulation":
            os.environ["TRADING_ENABLED"] = "false"
            os.environ["PAPER_TRADING"] = "true"
            os.environ["BACKTESTING_ENABLED"] = "false"
            os.environ["DRY_RUN"] = "true"
        else:
            logger.error(f"Unknown mode: {self.mode}")
            raise ValueError(f"Unknown mode: {self.mode}")

        logger.info(f"Environment variables set for {self.mode} mode:")
        logger.info(f"TRADING_ENABLED: {os.environ.get('TRADING_ENABLED')}")
        logger.info(f"PAPER_TRADING: {os.environ.get('PAPER_TRADING')}")
        logger.info(f"BACKTESTING_ENABLED: {os.environ.get('BACKTESTING_ENABLED')}")
        logger.info(f"DRY_RUN: {os.environ.get('DRY_RUN')}")

    async def initialize(self):
        """Initialize the unified runner."""
        # Load configuration
        try:
            # Load configuration from the config file
            self.config = load_config(self.config_path)
            logger.info(f"Loaded configuration from {self.config_path}")
            logger.info("Configuration loaded successfully")
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            raise

        # Initialize monitoring
        self.monitoring = get_monitoring_service()

        # Register components for health checks
        self.monitoring.register_component("config", lambda: self.config is not None)

        # Start health check server
        self.health_server = start_health_server(host="0.0.0.0", port=8080)
        logger.info("Health check server started on port 8080")

        # Set up Telegram alerts if credentials are available
        telegram_bot_token = os.environ.get("TELEGRAM_BOT_TOKEN")
        telegram_chat_id = os.environ.get("TELEGRAM_CHAT_ID")
        if telegram_bot_token and telegram_chat_id:
            # Set up alert handler with rate limiting (5 minutes between alerts of the same type)
            alert_handler = setup_telegram_alerts(telegram_bot_token, telegram_chat_id, rate_limit_seconds=300)

            # Register alert handlers for different alert types
            self.monitoring.register_alert_handler("component_unhealthy", alert_handler)
            self.monitoring.register_alert_handler("low_balance", alert_handler)
            self.monitoring.register_alert_handler("transaction_error", alert_handler)
            self.monitoring.register_alert_handler("circuit_breaker_open", alert_handler)
            self.monitoring.register_alert_handler("system_resources", alert_handler)

            # Initialize trading alerts
            try:
                from phase_4_deployment.utils.trading_alerts import get_trading_alerts

                # Get trading alerts instance
                trading_alerts = get_trading_alerts()

                # Store initial wallet balance
                wallet_balance = self.config.get("wallet", {}).get("initial_balance", 1000.0)
                trading_alerts.update_wallet_balance(wallet_balance, is_initial=True)

                # Send initial system status alert
                asyncio.create_task(trading_alerts.send_message(
                    f"🚀 *Synergy7 Trading System Started*\n\n"
                    f"*Mode*: {self.mode.upper()}\n"
                    f"*Time*: {datetime.now().isoformat()}\n"
                    f"*Initial Balance*: ${wallet_balance:.2f}\n\n"
                    f"The system is now running and will send alerts for trades, performance metrics, and system status."
                ))

                logger.info("Trading alerts initialized with initial wallet balance")
            except ImportError:
                logger.warning("Trading alerts module not available - performance metrics will not be sent")
            except Exception as e:
                logger.error(f"Error initializing trading alerts: {str(e)}")

            logger.info("Telegram alerts configured with rate limiting")
        else:
            logger.warning("Telegram alerts not configured - missing bot token or chat ID")

        # Start health checks
        self.monitoring.start_health_checks()
        logger.info("Monitoring and health checks started")

        # Start Streamlit dashboard if enabled
        if self.config.get("monitoring", {}).get("enabled", True):
            await self._start_streamlit_dashboard()

    async def _start_streamlit_dashboard(self):
        """Start the Streamlit dashboard."""
        try:
            # Check if Streamlit is installed
            subprocess.run(["streamlit", "--version"], check=True, capture_output=True)

            # Get Streamlit configuration
            streamlit_config = self.config.get("deployment", {}).get("streamlit", {})
            port = streamlit_config.get("port", 8501)
            headless = streamlit_config.get("headless", False)

            # Start Streamlit in a separate process
            cmd = [
                "streamlit", "run",
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "monitoring", "streamlit_dashboard.py"),
                "--server.port", str(port),
                "--server.headless", str(headless).lower(),
                "--server.enableCORS", "false",
                "--server.enableXsrfProtection", "false",
                "--browser.gatherUsageStats", "false",
            ]

            logger.info(f"Starting Streamlit dashboard: {' '.join(cmd)}")
            self.streamlit_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            logger.info(f"Streamlit dashboard started with PID {self.streamlit_process.pid}")
            logger.info(f"Streamlit dashboard URL: http://localhost:{port}")
        except subprocess.CalledProcessError:
            logger.warning("Streamlit not found. Please install it with: pip install streamlit")
        except Exception as e:
            logger.error(f"Error starting Streamlit dashboard: {str(e)}")

    async def run(self):
        """Run the unified runner in the specified mode."""
        logger.info(f"Running Synergy7 Trading System in {self.mode.upper()} mode")

        try:
            # Initialize components
            await self._initialize_components()

            if self.mode == "live" or self.mode == "paper":
                # Import and run the unified live trading module with signature verification fix
                if MODERN_TRANSACTION_AVAILABLE:
                    logger.info("🚀 Using modern live trading with signature verification fix")
                    await self._run_modern_live_trading()
                else:
                    logger.info("🚀 Using legacy live trading system")
                    await self._run_legacy_live_trading()
            elif self.mode == "backtest":
                # Import and run the backtest module
                try:
                    # Try to use the new VectorBT backtest runner
                    from phase_4_deployment.backtest.vectorbt_runner import VectorBTRunner

                    # Create and run the backtest
                    backtest_runner = VectorBTRunner(self.config.get('backtest', {}))

                    # Get the symbol and strategy from config
                    symbol = self.config.get('backtest', {}).get('symbol', 'SOL-USD')
                    strategy_id = self.config.get('strategies', [])[0].get('name', 'momentum_sol_usdc')

                    # Run the backtest
                    result = await asyncio.to_thread(backtest_runner.run_backtest, symbol, strategy_id)

                    if result.get('success', False):
                        logger.info(f"Backtest completed successfully: {result.get('metrics', {}).get('total_return_pct', 0):.2f}% return")
                    else:
                        logger.error(f"Backtest failed: {result.get('error', 'Unknown error')}")

                except ImportError:
                    # Fall back to the old backtest engine
                    logger.warning("VectorBT backtest runner not available, falling back to old backtest engine")
                    try:
                        from phase_2_backtest_engine.run_backtest import run_backtest
                        await run_backtest()
                    except ImportError:
                        logger.error("Backtest module not found. Please ensure phase_2_backtest_engine is installed.")
                        logger.info("Falling back to simulation mode...")
                        from phase_4_deployment.run_simulation import run_simulation
                        await run_simulation()
            elif self.mode == "simulation":
                # Import and run the simulation module
                from phase_4_deployment.run_simulation import run_simulation
                await run_simulation(
                    filter_chain=self.filter_chain,
                    signal_enricher=self.signal_enricher,
                    rl_data_collector=self.rl_data_collector
                )
            else:
                logger.error(f"Unknown mode: {self.mode}")
                raise ValueError(f"Unknown mode: {self.mode}")
        except Exception as e:
            logger.error(f"Error running {self.mode} mode: {str(e)}")
            raise

    async def _initialize_components(self):
        """Initialize the components needed for the trading system."""
        logger.info("Initializing components...")

        # Set dataset path for the new strategy module
        self.dataset_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'phase_2_backtest_engine/datasets/solana_meme_master'
        )

        # Load dataset metadata
        try:
            metadata_path = os.path.join(self.dataset_path, 'metadata/dataset_info.json')
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    self.dataset_metadata = json.load(f)
                logger.info(f"Loaded dataset metadata: {self.dataset_metadata.get('dataset_name')} v{self.dataset_metadata.get('version')}")
            else:
                logger.warning(f"Dataset metadata not found at {metadata_path}")
                self.dataset_metadata = {}
        except Exception as e:
            logger.error(f"Error loading dataset metadata: {str(e)}")
            self.dataset_metadata = {}

        # Initialize filter chain
        try:
            from phase_4_deployment.filters.filter_factory import FilterFactory

            # Create filter chain from configuration
            self.filter_chain = FilterFactory.create_filter_chain(self.config.get('filters', {}))
            logger.info(f"Initialized filter chain with {len(self.filter_chain.filters)} filters")
        except ImportError:
            logger.warning("Filter factory not available, filters will not be used")
            self.filter_chain = None

        # Initialize signal enricher
        try:
            from phase_4_deployment.signal_generator.signal_enricher import SignalEnricher

            # Create signal enricher from configuration
            self.signal_enricher = SignalEnricher(self.config.get('signal_enrichment', {}))
            logger.info(f"Initialized signal enricher with algorithm: {self.signal_enricher.ranking_algorithm}")
        except ImportError:
            logger.warning("Signal enricher not available, signals will not be enriched")
            self.signal_enricher = None

        # Initialize RL data collector
        try:
            from phase_4_deployment.rl_agent.data_collector import RLDataCollector

            # Create RL data collector from configuration
            self.rl_data_collector = RLDataCollector(self.config.get('rl_agent', {}))
            logger.info(f"Initialized RL data collector with data collection: {self.rl_data_collector.data_collection}")

            # Load training data from dataset outcomes
            if self.rl_data_collector.data_collection:
                try:
                    # Load outcome data for training
                    outcome_files = [
                        os.path.join(self.dataset_path, 'outcomes/momentum_sol_usdc_outcomes.json'),
                        os.path.join(self.dataset_path, 'outcomes/momentum_jup_usdc_outcomes.json'),
                        os.path.join(self.dataset_path, 'outcomes/mean_reversion_bonk_usdc_outcomes.json')
                    ]

                    training_pairs = []
                    for file_path in outcome_files:
                        if os.path.exists(file_path):
                            with open(file_path, 'r') as f:
                                data = json.load(f)
                                outcomes = data.get('outcomes', [])

                                # Load corresponding signals
                                strategy_id = os.path.basename(file_path).replace('_outcomes.json', '')
                                signal_path = os.path.join(self.dataset_path, f'signals/{strategy_id}_signals.json')

                                if os.path.exists(signal_path):
                                    with open(signal_path, 'r') as sf:
                                        signal_data = json.load(sf)
                                        signals = signal_data.get('signals', [])

                                        # Create signal-outcome pairs
                                        signal_dict = {signal.get('timestamp'): signal for signal in signals}

                                        for outcome in outcomes:
                                            signal_timestamp = outcome.get('signal_timestamp')
                                            if signal_timestamp in signal_dict:
                                                signal = signal_dict[signal_timestamp]
                                                training_pairs.append((signal, outcome))

                    # Store training pairs in RL data collector
                    self.rl_data_collector.pairs = training_pairs
                    logger.info(f"Loaded {len(training_pairs)} signal-outcome pairs for RL training")
                except Exception as e:
                    logger.error(f"Error loading outcome data for RL training: {str(e)}")
        except ImportError:
            logger.warning("RL data collector not available, RL data will not be collected")
            self.rl_data_collector = None

        logger.info("Components initialized successfully")

    async def _run_modern_live_trading(self):
        """Run modern live trading with signature verification fix."""
        try:
            logger.info("🚀 Starting modern live trading with signature verification fix")

            # Initialize modern transaction executor
            config = {
                'primary_rpc': f"https://mainnet.helius-rpc.com/?api-key={os.getenv('HELIUS_API_KEY')}",
                'fallback_rpc': 'https://api.mainnet-beta.solana.com',
                'jito_rpc': 'https://mainnet.block-engine.jito.wtf/api/v1',
                'helius_api_key': os.getenv('HELIUS_API_KEY')
            }

            modern_executor = ModernTransactionExecutor(config)
            await modern_executor.initialize()

            # Initialize Jupiter client with signature verification fix
            from solders.keypair import Keypair
            private_key = os.getenv('WALLET_PRIVATE_KEY')
            if not private_key:
                logger.error("❌ WALLET_PRIVATE_KEY not found for modern trading")
                return

            keypair = Keypair.from_base58_string(private_key)
            jupiter_client = OptimizedTransactionBuilder(
                rpc_url=config['primary_rpc'],
                keypair=keypair
            )

            logger.info("✅ Modern components initialized - signature verification fix active")

            # Import and run the signal generation and trading loop
            from scripts.unified_live_trading import UnifiedLiveTrader

            # Create trader instance
            trader = UnifiedLiveTrader()

            # Override with modern components
            trader.modern_executor = modern_executor
            trader.jupiter_client = jupiter_client

            # Initialize trader components
            if await trader.initialize_components():
                logger.info("✅ Modern live trader initialized successfully")

                # Run trading loop
                await trader.run_trading_loop()
            else:
                logger.error("❌ Failed to initialize modern live trader")

        except Exception as e:
            logger.error(f"❌ Error in modern live trading: {e}")
            raise

    async def _run_legacy_live_trading(self):
        """Run legacy live trading system."""
        try:
            logger.info("🚀 Starting legacy live trading system")

            # Import and run the legacy unified live trading
            from scripts.unified_live_trading import UnifiedLiveTrader

            trader = UnifiedLiveTrader()

            if await trader.initialize_components():
                logger.info("✅ Legacy live trader initialized successfully")
                await trader.run_trading_loop()
            else:
                logger.error("❌ Failed to initialize legacy live trader")

        except Exception as e:
            logger.error(f"❌ Error in legacy live trading: {e}")
            raise

    async def shutdown(self):
        """Shutdown the unified runner."""
        logger.info("Shutting down unified runner")

        # Stop Streamlit dashboard
        if self.streamlit_process:
            logger.info(f"Stopping Streamlit dashboard (PID {self.streamlit_process.pid})")
            self.streamlit_process.terminate()
            try:
                self.streamlit_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                logger.warning("Streamlit dashboard did not terminate gracefully, killing it")
                self.streamlit_process.kill()
            self.streamlit_process = None

        # Clean up filter chain
        if hasattr(self, 'filter_chain') and self.filter_chain:
            logger.info("Cleaning up filter chain")
            try:
                from phase_4_deployment.filters.filter_factory import FilterFactory
                await FilterFactory.close_filters(self.filter_chain)
            except Exception as e:
                logger.error(f"Error closing filters: {str(e)}")
            self.filter_chain = None

        # Clean up RL data collector
        if hasattr(self, 'rl_data_collector') and self.rl_data_collector:
            logger.info("Cleaning up RL data collector")
            try:
                self.rl_data_collector.clear_memory()
            except Exception as e:
                logger.error(f"Error clearing RL data collector memory: {str(e)}")
            self.rl_data_collector = None

        # Clean up signal enricher
        if hasattr(self, 'signal_enricher'):
            self.signal_enricher = None

        # Stop health check server
        if self.health_server:
            logger.info("Stopping health check server")
            # Health server is running in a background thread, no need to explicitly close it
            self.health_server = None

        # Stop monitoring
        if self.monitoring:
            logger.info("Stopping monitoring")
            self.monitoring.stop_health_checks()
            self.monitoring = None

        # Close trading alerts
        try:
            from phase_4_deployment.utils.trading_alerts import get_trading_alerts
            trading_alerts = get_trading_alerts()

            # Send shutdown message
            try:
                # Create a new event loop for the shutdown message
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # Send shutdown message
                loop.run_until_complete(trading_alerts.send_message(
                    f"🛑 *Synergy7 Trading System Shutdown*\n\n"
                    f"*Mode*: {self.mode.upper()}\n"
                    f"*Time*: {datetime.now().isoformat()}\n\n"
                    f"The system has been shut down."
                ))

                # Close the HTTP client
                loop.run_until_complete(trading_alerts.close())

                # Close the loop
                loop.close()
            except Exception as e:
                logger.error(f"Error sending shutdown message: {str(e)}")
        except ImportError:
            logger.debug("Trading alerts module not available")
        except Exception as e:
            logger.error(f"Error closing trading alerts: {str(e)}")

        logger.info("Unified runner shutdown complete")

async def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Unified Runner for Synergy7 Trading System")
    parser.add_argument("--mode", choices=["live", "paper", "backtest", "simulation"], default="paper",
                        help="Operational mode (default: paper)")
    parser.add_argument("--config", default="config.yaml",
                        help="Path to the configuration file (default: config.yaml)")
    parser.add_argument("--env", default=".env",
                        help="Path to the environment file (default: .env)")

    args = parser.parse_args()

    # Create and initialize the unified runner
    runner = UnifiedRunner(args.mode, args.config, args.env)

    try:
        # Initialize the runner
        await runner.initialize()

        # Run the runner
        await runner.run()
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Error in unified runner: {str(e)}")
    finally:
        # Shutdown the runner
        await runner.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
