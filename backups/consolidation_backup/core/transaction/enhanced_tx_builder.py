"""
Enhanced Transaction Builder with Modern Solana Best Practices
Addresses signature verification failures and RPC endpoint issues.
"""

import asyncio
import logging
import time
import base64
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

import httpx
from solders.keypair import Keypair
from solders.transaction import VersionedTransaction
from solders.message import MessageV0
from solders.hash import Hash
from solders.instruction import Instruction
from solders.compute_budget import set_compute_unit_limit, set_compute_unit_price

logger = logging.getLogger(__name__)

class EnhancedTransactionBuilder:
    """Enhanced transaction builder with modern Solana best practices."""
    
    def __init__(self, rpc_url: str, keypair: Keypair, backup_rpcs: List[str] = None):
        """
        Initialize enhanced transaction builder.
        
        Args:
            rpc_url: Primary RPC URL
            keypair: Wallet keypair for signing
            backup_rpcs: List of backup RPC URLs
        """
        self.rpc_url = rpc_url
        self.keypair = keypair
        self.backup_rpcs = backup_rpcs or []
        self.http_client = None
        
        # Transaction configuration
        self.max_blockhash_age_seconds = 60  # Fresh blockhash requirement
        self.default_compute_unit_limit = 200_000
        self.priority_fee_multiplier = 1.5  # Aggressive priority fees
        
        logger.info("✅ Enhanced transaction builder initialized")
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.http_client = httpx.AsyncClient(timeout=30.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.http_client:
            await self.http_client.aclose()
    
    async def get_fresh_blockhash(self) -> Optional[Hash]:
        """
        Get a fresh blockhash with retry logic across multiple RPCs.
        
        Returns:
            Fresh blockhash or None if failed
        """
        rpcs_to_try = [self.rpc_url] + self.backup_rpcs
        
        for rpc_url in rpcs_to_try:
            try:
                logger.info(f"🔄 Fetching fresh blockhash from {rpc_url[:50]}...")
                
                payload = {
                    'jsonrpc': '2.0',
                    'id': 1,
                    'method': 'getLatestBlockhash',
                    'params': [{'commitment': 'confirmed'}]
                }
                
                response = await self.http_client.post(rpc_url, json=payload)
                response.raise_for_status()
                result = response.json()
                
                if 'result' in result and 'value' in result['result']:
                    blockhash_str = result['result']['value']['blockhash']
                    blockhash = Hash.from_string(blockhash_str)
                    
                    logger.info(f"✅ Fresh blockhash obtained: {blockhash_str[:16]}...")
                    return blockhash
                else:
                    logger.warning(f"⚠️ Invalid blockhash response from {rpc_url}")
                    
            except Exception as e:
                logger.warning(f"⚠️ Failed to get blockhash from {rpc_url}: {e}")
                continue
        
        logger.error("❌ Failed to get fresh blockhash from all RPCs")
        return None
    
    async def get_priority_fee_estimate(self) -> int:
        """
        Get current priority fee estimate based on network conditions.
        
        Returns:
            Priority fee in micro-lamports
        """
        try:
            # Try to get recent priority fees
            payload = {
                'jsonrpc': '2.0',
                'id': 1,
                'method': 'getRecentPrioritizationFees',
                'params': [
                    {
                        'lockedWritableAccounts': [
                            str(self.keypair.pubkey())
                        ]
                    }
                ]
            }
            
            response = await self.http_client.post(self.rpc_url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if 'result' in result and result['result']:
                fees = result['result']
                if fees:
                    # Get 75th percentile fee for competitive priority
                    sorted_fees = sorted([fee['prioritizationFee'] for fee in fees])
                    percentile_75 = sorted_fees[int(len(sorted_fees) * 0.75)]
                    
                    # Apply multiplier for aggressive priority
                    priority_fee = int(percentile_75 * self.priority_fee_multiplier)
                    
                    # Minimum fee of 1000 micro-lamports
                    priority_fee = max(priority_fee, 1000)
                    
                    logger.info(f"💰 Priority fee estimate: {priority_fee} micro-lamports")
                    return priority_fee
            
            # Fallback to default aggressive fee
            default_fee = 5000  # 5000 micro-lamports
            logger.info(f"💰 Using default priority fee: {default_fee} micro-lamports")
            return default_fee
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to get priority fee estimate: {e}")
            # Fallback to conservative but competitive fee
            return 3000
    
    async def simulate_transaction(self, transaction: VersionedTransaction) -> bool:
        """
        Simulate transaction before sending to catch errors early.
        
        Args:
            transaction: Transaction to simulate
            
        Returns:
            True if simulation successful, False otherwise
        """
        try:
            # Serialize transaction for simulation
            tx_bytes = bytes(transaction)
            tx_base64 = base64.b64encode(tx_bytes).decode('utf-8')
            
            payload = {
                'jsonrpc': '2.0',
                'id': 1,
                'method': 'simulateTransaction',
                'params': [
                    tx_base64,
                    {
                        'encoding': 'base64',
                        'commitment': 'confirmed',
                        'replaceRecentBlockhash': True,
                        'sigVerify': True
                    }
                ]
            }
            
            response = await self.http_client.post(self.rpc_url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if 'result' in result:
                sim_result = result['result']
                
                if sim_result.get('value', {}).get('err') is None:
                    logger.info("✅ Transaction simulation successful")
                    
                    # Log compute units used
                    units_consumed = sim_result.get('value', {}).get('unitsConsumed', 0)
                    logger.info(f"🔧 Compute units consumed: {units_consumed}")
                    
                    return True
                else:
                    error = sim_result.get('value', {}).get('err')
                    logger.error(f"❌ Transaction simulation failed: {error}")
                    return False
            else:
                logger.error(f"❌ Invalid simulation response: {result}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error simulating transaction: {e}")
            return False
    
    async def build_jupiter_transaction_enhanced(self, signal: Dict[str, Any]) -> Optional[VersionedTransaction]:
        """
        Build Jupiter transaction with enhanced features.
        
        Args:
            signal: Trading signal
            
        Returns:
            Enhanced VersionedTransaction or None if failed
        """
        try:
            logger.info(f"🔨 Building enhanced Jupiter transaction for {signal.get('market', 'Unknown')}")
            
            # Step 1: Get Jupiter quote and transaction
            jupiter_tx_data = await self._get_jupiter_transaction_data(signal)
            if not jupiter_tx_data:
                logger.error("❌ Failed to get Jupiter transaction data")
                return None
            
            # Step 2: Deserialize Jupiter transaction
            tx_bytes = base64.b64decode(jupiter_tx_data)
            transaction = VersionedTransaction.from_bytes(tx_bytes)
            
            # Step 3: Get fresh blockhash
            fresh_blockhash = await self.get_fresh_blockhash()
            if not fresh_blockhash:
                logger.error("❌ Failed to get fresh blockhash")
                return None
            
            # Step 4: Get priority fee estimate
            priority_fee = await self.get_priority_fee_estimate()
            
            # Step 5: Enhance transaction with priority fees and fresh blockhash
            enhanced_tx = await self._enhance_transaction(
                transaction, 
                fresh_blockhash, 
                priority_fee
            )
            
            if not enhanced_tx:
                logger.error("❌ Failed to enhance transaction")
                return None
            
            # Step 6: Simulate transaction
            if not await self.simulate_transaction(enhanced_tx):
                logger.error("❌ Transaction simulation failed")
                return None
            
            logger.info("✅ Enhanced Jupiter transaction built successfully")
            return enhanced_tx
            
        except Exception as e:
            logger.error(f"❌ Error building enhanced Jupiter transaction: {e}")
            return None
    
    async def _get_jupiter_transaction_data(self, signal: Dict[str, Any]) -> Optional[str]:
        """Get transaction data from Jupiter API."""
        try:
            # Import existing Jupiter integration
            from core.dex.orca_swap_builder import OrcaSwapBuilder
            
            # Use existing Jupiter integration from Orca builder
            builder = OrcaSwapBuilder(str(self.keypair.pubkey()))
            await builder.initialize()
            
            # Build transaction using existing logic
            result = await builder.build_swap_transaction(signal)
            
            if result and result.get('success'):
                return result.get('transaction')
            else:
                logger.error(f"❌ Jupiter transaction building failed: {result.get('error', 'Unknown error')}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error getting Jupiter transaction data: {e}")
            return None
    
    async def _enhance_transaction(self, transaction: VersionedTransaction, 
                                 fresh_blockhash: Hash, priority_fee: int) -> Optional[VersionedTransaction]:
        """
        Enhance transaction with fresh blockhash and priority fees.
        
        Args:
            transaction: Original transaction
            fresh_blockhash: Fresh blockhash
            priority_fee: Priority fee in micro-lamports
            
        Returns:
            Enhanced transaction or None if failed
        """
        try:
            # Extract message from transaction
            message = transaction.message
            
            # Create priority fee instructions
            priority_instructions = [
                set_compute_unit_limit(self.default_compute_unit_limit),
                set_compute_unit_price(priority_fee)
            ]
            
            # Combine with existing instructions
            all_instructions = priority_instructions + list(message.instructions)
            
            # Create new message with fresh blockhash
            enhanced_message = MessageV0.try_compile(
                payer=self.keypair.pubkey(),
                instructions=all_instructions,
                address_lookup_table_accounts=[],
                recent_blockhash=fresh_blockhash
            )
            
            # Create and sign enhanced transaction
            enhanced_tx = VersionedTransaction(enhanced_message, [self.keypair])
            
            logger.info(f"✅ Transaction enhanced with priority fee: {priority_fee} micro-lamports")
            return enhanced_tx
            
        except Exception as e:
            logger.error(f"❌ Error enhancing transaction: {e}")
            return None
