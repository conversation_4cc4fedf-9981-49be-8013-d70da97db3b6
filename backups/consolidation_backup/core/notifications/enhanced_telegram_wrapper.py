#!/usr/bin/env python3
"""
Enhanced Telegram Wrapper
Combines original TelegramNotifier with DualTelegramNotifier for backward compatibility
while adding dual-chat functionality for trade alerts.
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class EnhancedTelegramWrapper:
    """
    Enhanced wrapper that combines original and dual Telegram functionality.

    - Trade alerts go to BOTH chats (original + new chat ID)
    - Profitability analysis goes ONLY to original chat
    - System alerts go to BOTH chats
    """

    def __init__(self, bot_token: Optional[str] = None, primary_chat_id: Optional[str] = None):
        """Initialize enhanced Telegram wrapper."""
        self.bot_token = bot_token or os.getenv('TELEGRAM_BOT_TOKEN')
        self.primary_chat_id = primary_chat_id or os.getenv('TELEGRAM_CHAT_ID')
        self.secondary_chat_id = "-1002232263415"  # New chat ID for trade alerts (with group prefix)

        # Initialize both notifiers
        self._init_notifiers()

        # Session tracking
        self.session_start_balance = None

    def _init_notifiers(self):
        """Initialize original and dual notifiers."""
        try:
            # Import original notifier
            from core.notifications.telegram_notifier import TelegramNotifier
            self.original_notifier = TelegramNotifier(self.bot_token, self.primary_chat_id)

            # Import dual notifier
            from core.notifications.dual_telegram_notifier import DualTelegramNotifier
            self.dual_notifier = DualTelegramNotifier(
                self.bot_token,
                self.primary_chat_id,
                self.secondary_chat_id
            )

            self.enabled = self.original_notifier.enabled

            if self.enabled:
                logger.info("✅ Enhanced Telegram wrapper initialized")
                logger.info(f"  Primary chat (all alerts): {self.primary_chat_id}")
                logger.info(f"  Secondary chat (trade alerts only): {self.secondary_chat_id}")
            else:
                logger.warning("⚠️ Telegram credentials not found - notifications disabled")

        except ImportError as e:
            logger.error(f"Failed to import Telegram notifiers: {e}")
            self.enabled = False

    def set_session_start_balance(self, balance: float):
        """Set session start balance for both notifiers."""
        self.session_start_balance = balance

        if hasattr(self, 'original_notifier'):
            self.original_notifier.set_session_start_balance(balance)

        if hasattr(self, 'dual_notifier'):
            self.dual_notifier.set_session_start_balance(balance)

    async def notify_trade_executed(self, trade_data: Dict[str, Any]) -> bool:
        """
        Send trade execution notification to BOTH chats.
        This is the key function that duplicates trade alerts.
        """
        if not self.enabled:
            logger.debug("Telegram disabled - skipping trade notification")
            return False

        try:
            # Use dual notifier to send to both chats
            success = await self.dual_notifier.notify_trade_executed(trade_data)

            if success:
                logger.info("✅ Trade alert sent to both Telegram chats")
            else:
                logger.warning("⚠️ Failed to send trade alert to one or more chats")

            return success

        except Exception as e:
            logger.error(f"Error sending dual trade notification: {e}")

            # Fallback to original notifier
            try:
                logger.info("Attempting fallback to original notifier...")
                return await self.original_notifier.notify_trade_executed(trade_data)
            except Exception as fallback_error:
                logger.error(f"Fallback also failed: {fallback_error}")
                return False

    async def notify_profitability_analysis(self, message: str) -> bool:
        """
        Send profitability analysis ONLY to primary chat.
        This keeps analysis separate from trade alerts.
        """
        if not self.enabled:
            logger.debug("Telegram disabled - skipping profitability analysis")
            return False

        try:
            # Use original notifier to send only to primary chat
            success = await self.original_notifier.send_message(message)

            if success:
                logger.info("✅ Profitability analysis sent to primary chat only")
            else:
                logger.warning("⚠️ Failed to send profitability analysis")

            return success

        except Exception as e:
            logger.error(f"Error sending profitability analysis: {e}")
            return False

    async def notify_pnl_milestone(self, pnl_metrics: Dict[str, Any], milestone_type: str) -> bool:
        """Send PnL milestone to BOTH chats."""
        if not self.enabled:
            return False

        try:
            return await self.dual_notifier.notify_pnl_milestone(pnl_metrics, milestone_type)
        except Exception as e:
            logger.error(f"Error sending PnL milestone: {e}")
            # Fallback to original
            try:
                return await self.original_notifier.notify_pnl_milestone(pnl_metrics, milestone_type)
            except:
                return False

    async def notify_system_status(self, status: str, details: str = "") -> bool:
        """Send system status to BOTH chats."""
        if not self.enabled:
            return False

        try:
            return await self.dual_notifier.notify_system_status(status, details)
        except Exception as e:
            logger.error(f"Error sending system status: {e}")
            return False

    async def notify_error(self, component: str, error_message: str) -> bool:
        """Send error notification to BOTH chats."""
        if not self.enabled:
            return False

        try:
            return await self.original_notifier.notify_error(component, error_message)
        except Exception as e:
            logger.error(f"Error sending error notification: {e}")
            return False

    async def send_message(self, message: str, parse_mode: str = "Markdown") -> bool:
        """Send generic message to primary chat only (for backward compatibility)."""
        if not self.enabled:
            return False

        try:
            return await self.original_notifier.send_message(message, parse_mode)
        except Exception as e:
            logger.error(f"Error sending generic message: {e}")
            return False

    async def test_connection(self) -> bool:
        """Test connection to both chats."""
        if not self.enabled:
            logger.warning("Telegram not configured")
            return False

        try:
            # Test dual notifier
            dual_results = await self.dual_notifier.test_connection()

            success_count = sum(dual_results.values())
            total_chats = len(dual_results)

            logger.info(f"Telegram test results: {success_count}/{total_chats} chats successful")

            return success_count > 0

        except Exception as e:
            logger.error(f"Error testing Telegram connection: {e}")
            return False

    async def close(self):
        """Close all notifiers."""
        try:
            if hasattr(self, 'original_notifier'):
                await self.original_notifier.close()

            if hasattr(self, 'dual_notifier'):
                await self.dual_notifier.close()

        except Exception as e:
            logger.error(f"Error closing Telegram notifiers: {e}")


# Global instance for easy access
_enhanced_telegram_wrapper = None

def get_enhanced_telegram_wrapper() -> EnhancedTelegramWrapper:
    """Get the global enhanced Telegram wrapper instance."""
    global _enhanced_telegram_wrapper

    if _enhanced_telegram_wrapper is None:
        _enhanced_telegram_wrapper = EnhancedTelegramWrapper()

    return _enhanced_telegram_wrapper
