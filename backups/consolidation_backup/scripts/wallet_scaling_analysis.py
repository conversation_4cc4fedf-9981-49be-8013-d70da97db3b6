#!/usr/bin/env python3
"""
Wallet Scaling Analysis & Strategy

Analyzes the current system's scaling capabilities and provides recommendations
for optimal wallet allocation strategies.
"""

import os
import json
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

async def analyze_current_wallet_usage():
    """Analyze current wallet usage patterns."""
    print("💰 CURRENT WALLET USAGE ANALYSIS")
    print("=" * 50)
    
    try:
        from phase_4_deployment.rpc_execution.helius_client import HeliusClient
        
        helius_api_key = os.getenv('HELIUS_API_KEY')
        wallet_address = os.getenv('WALLET_ADDRESS')
        
        if not helius_api_key or not wallet_address:
            print("❌ Missing API credentials")
            return None
        
        client = HeliusClient(api_key=helius_api_key)
        balance_data = await client.get_balance(wallet_address)
        await client.close()
        
        if isinstance(balance_data, dict) and 'balance_sol' in balance_data:
            current_balance = balance_data['balance_sol']
            print(f"✅ Current Wallet Balance: {current_balance:.6f} SOL")
            
            # Analyze recent trade records
            trades_dir = "output/live_production/trades"
            if os.path.exists(trades_dir):
                trade_files = [f for f in os.listdir(trades_dir) if f.endswith('.json')]
                
                if trade_files:
                    print(f"📊 Found {len(trade_files)} trade records")
                    
                    # Analyze trade sizes
                    trade_sizes = []
                    for trade_file in trade_files[-10:]:  # Last 10 trades
                        try:
                            with open(os.path.join(trades_dir, trade_file), 'r') as f:
                                trade_data = json.load(f)
                            
                            if 'signal' in trade_data and 'size' in trade_data['signal']:
                                trade_sizes.append(trade_data['signal']['size'])
                        except Exception:
                            continue
                    
                    if trade_sizes:
                        avg_trade_size = sum(trade_sizes) / len(trade_sizes)
                        max_trade_size = max(trade_sizes)
                        min_trade_size = min(trade_sizes)
                        
                        print(f"📈 Average Trade Size: {avg_trade_size:.6f} SOL")
                        print(f"📈 Max Trade Size: {max_trade_size:.6f} SOL")
                        print(f"📈 Min Trade Size: {min_trade_size:.6f} SOL")
                        print(f"📈 Trade Size as % of Wallet: {(avg_trade_size/current_balance)*100:.2f}%")
                        
                        return {
                            'current_balance': current_balance,
                            'avg_trade_size': avg_trade_size,
                            'max_trade_size': max_trade_size,
                            'min_trade_size': min_trade_size,
                            'trade_size_pct': (avg_trade_size/current_balance)*100
                        }
            
            return {'current_balance': current_balance}
            
    except Exception as e:
        print(f"❌ Error analyzing wallet usage: {e}")
        return None

def analyze_scaling_parameters():
    """Analyze current scaling parameters from config."""
    print("\n🔧 SCALING PARAMETERS ANALYSIS")
    print("=" * 50)
    
    # Current config parameters
    scaling_params = {
        'max_position_size_pct': 0.5,  # 50% from config
        'max_position_size_usd': 50000,
        'max_single_asset_pct': 0.15,  # 15%
        'max_sector_exposure_pct': 0.4,  # 40%
        'min_position_size': 0.01,
        'var_target_pct': 0.01,  # 1% VaR target
        'daily_loss_limit_usd': 5000,
        'max_drawdown_pct': 0.15  # 15%
    }
    
    print("📋 CURRENT SCALING PARAMETERS:")
    for param, value in scaling_params.items():
        if 'pct' in param:
            print(f"   {param}: {value*100:.1f}%")
        else:
            print(f"   {param}: {value}")
    
    return scaling_params

def calculate_scaling_scenarios(current_balance, scaling_params):
    """Calculate different scaling scenarios."""
    print("\n📊 WALLET SCALING SCENARIOS")
    print("=" * 50)
    
    sol_price_usd = 180.0  # Approximate SOL price
    current_balance_usd = current_balance * sol_price_usd
    
    scenarios = {
        'conservative_half': {
            'active_capital_pct': 0.5,
            'max_position_pct': 0.1,  # 10% of active capital
            'description': 'Conservative: Use 50% of wallet, 10% max position'
        },
        'moderate_half': {
            'active_capital_pct': 0.5,
            'max_position_pct': 0.2,  # 20% of active capital
            'description': 'Moderate: Use 50% of wallet, 20% max position'
        },
        'aggressive_half': {
            'active_capital_pct': 0.5,
            'max_position_pct': 0.3,  # 30% of active capital
            'description': 'Aggressive: Use 50% of wallet, 30% max position'
        },
        'conservative_full': {
            'active_capital_pct': 1.0,
            'max_position_pct': 0.05,  # 5% of full wallet
            'description': 'Conservative Full: Use 100% of wallet, 5% max position'
        },
        'moderate_full': {
            'active_capital_pct': 1.0,
            'max_position_pct': 0.1,  # 10% of full wallet
            'description': 'Moderate Full: Use 100% of wallet, 10% max position'
        },
        'aggressive_full': {
            'active_capital_pct': 1.0,
            'max_position_pct': 0.15,  # 15% of full wallet
            'description': 'Aggressive Full: Use 100% of wallet, 15% max position'
        }
    }
    
    print("🎯 SCALING SCENARIO ANALYSIS:")
    print()
    
    for scenario_name, scenario in scenarios.items():
        active_capital = current_balance * scenario['active_capital_pct']
        active_capital_usd = active_capital * sol_price_usd
        max_position_sol = active_capital * scenario['max_position_pct']
        max_position_usd = max_position_sol * sol_price_usd
        
        print(f"📈 {scenario['description']}")
        print(f"   Active Capital: {active_capital:.3f} SOL (${active_capital_usd:,.0f})")
        print(f"   Max Position: {max_position_sol:.3f} SOL (${max_position_usd:,.0f})")
        print(f"   Max Daily Trades: {int(active_capital / max_position_sol)}")
        print(f"   Risk per Trade: {scenario['max_position_pct']*100:.1f}% of active capital")
        print()
    
    return scenarios

def analyze_risk_scaling():
    """Analyze risk scaling mechanisms."""
    print("⚠️ RISK SCALING ANALYSIS")
    print("=" * 50)
    
    risk_factors = {
        'volatility_scaling': {
            'enabled': True,
            'description': 'Reduces position size during high volatility',
            'impact': 'Can reduce positions by 20-50% in volatile markets'
        },
        'regime_based_sizing': {
            'enabled': True,
            'description': 'Adjusts position size based on market regime',
            'impact': 'Trending: 100%, Ranging: 75%, Volatile: 50%'
        },
        'var_based_sizing': {
            'enabled': True,
            'description': 'Uses VaR calculations for position sizing',
            'impact': 'Targets 1% portfolio VaR per trade'
        },
        'correlation_adjustment': {
            'enabled': True,
            'description': 'Reduces position size for correlated assets',
            'impact': 'Can reduce positions by 50% for highly correlated assets'
        }
    }
    
    print("🛡️ RISK SCALING MECHANISMS:")
    for factor, details in risk_factors.items():
        status = "✅ ENABLED" if details['enabled'] else "❌ DISABLED"
        print(f"   {factor}: {status}")
        print(f"      {details['description']}")
        print(f"      Impact: {details['impact']}")
        print()

def generate_scaling_recommendations(wallet_analysis, scenarios):
    """Generate scaling recommendations."""
    print("🎯 SCALING RECOMMENDATIONS")
    print("=" * 50)
    
    current_balance = wallet_analysis.get('current_balance', 3.1)
    current_balance_usd = current_balance * 180.0
    
    print("📋 RECOMMENDED SCALING STRATEGY:")
    print()
    
    # Phase 1: Start with half wallet (Current approach)
    print("🔸 PHASE 1: HALF-WALLET STRATEGY (CURRENT)")
    print("   ✅ Use 50% of wallet balance for active trading")
    print("   ✅ Keep 50% as reserve for opportunities and safety")
    print("   ✅ Max position: 10-20% of active capital")
    print("   ✅ Ideal for: Learning system behavior, building confidence")
    print("   ✅ Risk level: Conservative to Moderate")
    print()
    
    # Phase 2: Gradual scaling conditions
    print("🔸 PHASE 2: SCALING CONDITIONS")
    print("   📈 Scale to 75% wallet when:")
    print("      - Consistent profitability for 30+ days")
    print("      - Max drawdown < 5% over 30 days")
    print("      - Sharpe ratio > 1.0")
    print("      - Win rate > 55%")
    print()
    
    # Phase 3: Full wallet conditions
    print("🔸 PHASE 3: FULL-WALLET CONDITIONS")
    print("   📈 Scale to 100% wallet when:")
    print("      - Consistent profitability for 60+ days")
    print("      - Max drawdown < 3% over 60 days")
    print("      - Sharpe ratio > 1.5")
    print("      - Win rate > 60%")
    print("      - Portfolio value > $10,000")
    print()
    
    # Risk management scaling
    print("🔸 RISK MANAGEMENT SCALING:")
    print("   ⚠️ Reduce to 25% wallet if:")
    print("      - Daily loss > 2%")
    print("      - Weekly loss > 5%")
    print("      - Monthly loss > 10%")
    print("   ⚠️ Stop trading if:")
    print("      - Daily loss > 5%")
    print("      - Weekly loss > 10%")
    print("      - Monthly loss > 20%")
    print()
    
    # Optimal scaling timeline
    print("🔸 OPTIMAL SCALING TIMELINE:")
    print("   Week 1-2: 25% wallet (learning phase)")
    print("   Week 3-4: 50% wallet (current phase)")
    print("   Month 2-3: 75% wallet (if conditions met)")
    print("   Month 4+: 100% wallet (if conditions met)")

async def main():
    """Main analysis function."""
    print("💰 WALLET SCALING ANALYSIS & STRATEGY")
    print("=" * 60)
    print("Analyzing current system scaling capabilities and recommendations")
    print("=" * 60)
    print(f"🕐 Analysis started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Analyze current wallet usage
    wallet_analysis = await analyze_current_wallet_usage()
    
    if not wallet_analysis:
        print("❌ Could not analyze wallet - using defaults")
        wallet_analysis = {'current_balance': 3.1}
    
    # Analyze scaling parameters
    scaling_params = analyze_scaling_parameters()
    
    # Calculate scaling scenarios
    scenarios = calculate_scaling_scenarios(
        wallet_analysis['current_balance'], 
        scaling_params
    )
    
    # Analyze risk scaling
    analyze_risk_scaling()
    
    # Generate recommendations
    generate_scaling_recommendations(wallet_analysis, scenarios)
    
    print("=" * 60)
    print("📊 SCALING ANALYSIS COMPLETE")
    print("=" * 60)
    print("🎯 Key Takeaways:")
    print("1. Current 50% wallet strategy is optimal for learning phase")
    print("2. System has robust risk scaling mechanisms")
    print("3. Gradual scaling based on performance metrics is recommended")
    print("4. Full wallet usage should only occur after proven profitability")
    print("5. Always maintain emergency reserves and stop-loss mechanisms")
    
    return 0

if __name__ == "__main__":
    exit(asyncio.run(main()))
