#!/usr/bin/env python3
"""
Comprehensive Alert System Test

Tests all alert systems including Telegram, email, and monitoring alerts
with integration to live trading systems.
"""

import asyncio
import os
import sys
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.notifications.telegram_notifier import TelegramNotifier

# Try to import optional components
try:
    from phase_4_deployment.monitoring.monitoring_service import MonitoringService
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False

try:
    from phase_4_deployment.utils.trading_alerts import get_trading_alerts
    TRADING_ALERTS_AVAILABLE = True
except ImportError:
    TRADING_ALERTS_AVAILABLE = False

async def test_telegram_alerts():
    """Test Telegram alert system."""
    print("🧪 Testing Telegram Alert System")
    print("=" * 50)

    notifier = TelegramNotifier()

    if not notifier.enabled:
        print("❌ Telegram not configured!")
        return False

    print(f"✅ Telegram configured for chat: {notifier.chat_id}")

    try:
        # Test 1: Connection test
        print("🔄 Testing connection...")
        if await notifier.test_connection():
            print("✅ Connection test successful!")
        else:
            print("❌ Connection test failed!")
            return False

        await asyncio.sleep(2)

        # Test 2: Live trading integration test
        print("🔄 Testing live trading integration...")
        sample_trade = {
            'signal': {
                'action': 'BUY',
                'size': 0.05,
                'confidence': 0.87,
                'price': 182.45,
                'strategy': 'Enhanced Momentum'
            },
            'position_data': {
                'position_size_sol': 0.05,
                'position_size_usd': 9.12,
                'total_wallet_sol': 3.952251432
            },
            'transaction_result': {
                'success': True,
                'signature': '5zdGrUpKJi6EbPL2mNxqVQKjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHj',
                'execution_time': 0.234
            },
            'timestamp': datetime.now().isoformat()
        }

        # Set session start balance for PnL tracking
        notifier.set_session_start_balance(3.947251432)

        if await notifier.notify_trade_executed(sample_trade):
            print("✅ Live trading integration successful!")
        else:
            print("❌ Live trading integration failed!")

        await asyncio.sleep(2)

        # Test 3: PnL milestone
        print("🔄 Testing PnL milestone alerts...")
        pnl_metrics = {
            'pnl_sol': 0.005,
            'pnl_usd': 0.91,
            'pnl_percent': 0.13,
            'current_balance': 3.952251432,
            'start_balance': 3.947251432
        }

        if await notifier.notify_pnl_milestone(pnl_metrics, "profit"):
            print("✅ PnL milestone alerts working!")
        else:
            print("❌ PnL milestone alerts failed!")

        await notifier.close()
        return True

    except Exception as e:
        print(f"❌ Error during Telegram testing: {e}")
        return False

async def test_monitoring_alerts():
    """Test monitoring service alerts."""
    print("\n🔍 Testing Monitoring Service Alerts")
    print("=" * 50)

    if not MONITORING_AVAILABLE:
        print("⚠️ Monitoring service not available - skipping test")
        return True

    try:
        # Initialize monitoring service
        monitoring = MonitoringService()

        # Add Telegram alerter if configured
        bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        chat_id = os.getenv('TELEGRAM_CHAT_ID')

        if bot_token and chat_id:
            monitoring.add_telegram_alerter(bot_token, chat_id)
            print("✅ Telegram alerter added to monitoring service")
        else:
            print("⚠️ Telegram not configured for monitoring service")

        # Test component registration
        def test_component_healthy():
            return True

        def test_component_unhealthy():
            return False

        monitoring.register_component("test_healthy", test_component_healthy)
        monitoring.register_component("test_unhealthy", test_component_unhealthy)
        print("✅ Test components registered")

        # Run health checks
        print("🔄 Running health checks...")
        health_results = await monitoring.check_health()

        healthy_count = sum(1 for result in health_results.values() if result)
        total_count = len(health_results)

        print(f"📊 Health check results: {healthy_count}/{total_count} components healthy")

        if health_results.get("test_healthy") and not health_results.get("test_unhealthy"):
            print("✅ Health checks working correctly!")
            return True
        else:
            print("❌ Health checks not working as expected!")
            return False

    except Exception as e:
        print(f"❌ Error during monitoring testing: {e}")
        return False

async def test_trading_alerts():
    """Test trading alerts system."""
    print("\n📈 Testing Trading Alerts System")
    print("=" * 50)

    if not TRADING_ALERTS_AVAILABLE:
        print("⚠️ Trading alerts not available - skipping test")
        return True

    try:
        # Get trading alerts instance
        trading_alerts = get_trading_alerts()
        print("✅ Trading alerts system initialized")

        # Test trade notification
        print("🔄 Testing trade notification...")
        sample_trade = {
            'signal': {
                'action': 'SELL',
                'market': 'SOL-USDC',
                'size': 0.03,
                'confidence': 0.92,
                'price': 183.21
            },
            'position_data': {
                'position_size_sol': 0.03,
                'position_size_usd': 5.50
            },
            'transaction_result': {
                'success': True,
                'signature': '3xdGrUpKJi6EbPL2mNxqVQKjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHj',
                'execution_time': 0.187
            }
        }

        if await trading_alerts.send_trade_notification(sample_trade):
            print("✅ Trade notification sent successfully!")
        else:
            print("❌ Trade notification failed!")

        # Test metrics alert
        print("🔄 Testing metrics alert...")
        if await trading_alerts.send_performance_metrics():
            print("✅ Metrics alert sent successfully!")
        else:
            print("❌ Metrics alert failed!")

        return True

    except Exception as e:
        print(f"❌ Error during trading alerts testing: {e}")
        return False

def test_live_trading_integration():
    """Test integration with live trading scripts."""
    print("\n🔗 Testing Live Trading Integration")
    print("=" * 50)

    # Check for live trading scripts
    live_trading_scripts = [
        "scripts/unified_live_trading.py",
        "scripts/start_live_production.py",
        "scripts/rl_enhanced_live_trading.py",
        "phase_4_deployment/start_live_trading.py"
    ]

    integration_score = 0
    total_scripts = len(live_trading_scripts)

    for script_path in live_trading_scripts:
        if os.path.exists(script_path):
            print(f"✅ Found: {script_path}")

            # Check for Telegram integration
            try:
                with open(script_path, 'r') as f:
                    content = f.read()

                if "TelegramNotifier" in content or "telegram_notifier" in content:
                    print(f"  📱 Telegram integration: ✅")
                    integration_score += 1
                else:
                    print(f"  📱 Telegram integration: ❌")

            except Exception as e:
                print(f"  ❌ Error reading {script_path}: {e}")
        else:
            print(f"❌ Missing: {script_path}")

    integration_percentage = (integration_score / total_scripts) * 100
    print(f"\n📊 Integration Score: {integration_score}/{total_scripts} ({integration_percentage:.1f}%)")

    return integration_score >= total_scripts * 0.5  # At least 50% integration

async def test_alert_data_integration():
    """Test alert integration with live trading data."""
    print("\n📊 Testing Alert Data Integration")
    print("=" * 50)

    try:
        # Check if we have live trading data
        data_paths = [
            "output/enhanced_live_trading/latest_metrics.json",
            "output/live_production/production_metrics.json",
            "phase_4_deployment/output/enhanced_live_trading/latest_metrics.json"
        ]

        live_data = None
        for path in data_paths:
            if os.path.exists(path):
                with open(path, 'r') as f:
                    live_data = json.load(f)
                print(f"✅ Found live data: {path}")
                break

        if not live_data:
            print("⚠️ No live trading data found - generating test data...")
            # Use our test data
            live_data = {
                'session_duration_minutes': 45.2,
                'metrics': {
                    'cycles_completed': 25,
                    'trades_executed': 12,
                    'trades_rejected': 3
                },
                'executor_metrics': {
                    'success_rate': 0.8,
                    'total_volume_usd': 156.78
                }
            }

        # Test alert with real data
        notifier = TelegramNotifier()
        if notifier.enabled:
            print("🔄 Testing alert with live data...")

            # Create session end notification with real data
            success = await notifier.notify_session_ended(
                live_data.get('metrics', {}),
                final_balance=3.952251432,
                avg_price=182.45
            )

            if success:
                print("✅ Live data integration successful!")
                return True
            else:
                print("❌ Live data integration failed!")
                return False
        else:
            print("⚠️ Telegram not configured - skipping live data test")
            return True

    except Exception as e:
        print(f"❌ Error during data integration testing: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 COMPREHENSIVE ALERT SYSTEM TEST")
    print("=" * 60)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Test results
    results = {}

    # Test 1: Telegram Alerts
    results['telegram'] = await test_telegram_alerts()

    # Test 2: Monitoring Alerts
    results['monitoring'] = await test_monitoring_alerts()

    # Test 3: Trading Alerts
    results['trading_alerts'] = await test_trading_alerts()

    # Test 4: Live Trading Integration
    results['integration'] = test_live_trading_integration()

    # Test 5: Alert Data Integration
    results['data_integration'] = await test_alert_data_integration()

    # Summary
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 60)

    passed_tests = sum(results.values())
    total_tests = len(results)

    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title()}: {status}")

    print(f"\n🎯 Overall Score: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")

    if passed_tests == total_tests:
        print("\n🎉 ALL ALERT SYSTEMS WORKING PERFECTLY!")
        print("🚀 Ready for live trading with comprehensive monitoring!")
        return 0
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ ALERT SYSTEMS MOSTLY WORKING!")
        print("⚠️ Minor issues detected - check failed tests above")
        return 0
    else:
        print("\n⚠️ ALERT SYSTEMS NEED ATTENTION!")
        print("❌ Multiple failures detected - please fix before live trading")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
