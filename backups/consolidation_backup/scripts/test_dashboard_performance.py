#!/usr/bin/env python3
"""
Dashboard Performance Test

Tests dashboard performance with Watchdog optimization.
"""

import time
import json
import os
from datetime import datetime

def test_file_monitoring_performance():
    """Test file monitoring performance with Watchdog."""
    print("🔍 Testing Dashboard File Monitoring Performance...")
    
    # Create test data
    test_data = {
        "session_start": datetime.now().isoformat(),
        "metrics": {
            "cycles_completed": 5,
            "trades_executed": 3,
            "success_rate": 0.6
        },
        "test_timestamp": datetime.now().isoformat()
    }
    
    # Test file write performance
    start_time = time.time()
    
    # Write test data multiple times to simulate live trading updates
    for i in range(10):
        test_data["update_count"] = i
        test_data["timestamp"] = datetime.now().isoformat()
        
        with open("output/enhanced_live_trading/latest_metrics.json", 'w') as f:
            json.dump(test_data, f, indent=2)
        
        time.sleep(0.1)  # Small delay between updates
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"✅ File update performance: {total_time:.2f} seconds for 10 updates")
    print(f"✅ Average update time: {total_time/10:.3f} seconds per update")
    
    return total_time < 2.0  # Should complete in under 2 seconds

def test_dashboard_responsiveness():
    """Test dashboard responsiveness indicators."""
    print("\n📊 Testing Dashboard Responsiveness...")
    
    # Check if dashboard is running
    import requests
    try:
        response = requests.get("http://localhost:8503", timeout=5)
        if response.status_code == 200:
            print("✅ Dashboard is responsive")
            return True
        else:
            print(f"❌ Dashboard returned status: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Dashboard not accessible: {e}")
        return False

def verify_watchdog_installation():
    """Verify Watchdog is properly installed and available."""
    print("\n🐕 Verifying Watchdog Installation...")
    
    try:
        from watchdog.observers import Observer
        from watchdog.events import FileSystemEventHandler
        print("✅ Watchdog Observer: Available")
        
        # Test creating an observer
        observer = Observer()
        print("✅ Watchdog Observer: Can be instantiated")
        
        # Check if it can monitor directories
        if os.path.exists("output"):
            print("✅ Watchdog: Can monitor output directory")
        
        return True
        
    except ImportError as e:
        print(f"❌ Watchdog import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Watchdog test failed: {e}")
        return False

def main():
    """Main performance test function."""
    print("🚀 DASHBOARD PERFORMANCE TEST WITH WATCHDOG")
    print("=" * 60)
    print("Testing dashboard performance improvements with Watchdog")
    print("=" * 60)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    results = {}
    
    # Test 1: Verify Watchdog installation
    results['watchdog_installation'] = verify_watchdog_installation()
    
    # Test 2: Test file monitoring performance
    results['file_monitoring'] = test_file_monitoring_performance()
    
    # Test 3: Test dashboard responsiveness
    results['dashboard_responsive'] = test_dashboard_responsiveness()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DASHBOARD PERFORMANCE TEST SUMMARY")
    print("=" * 60)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    percentage = (passed_tests / total_tests) * 100
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\n🎯 Performance Score: {passed_tests}/{total_tests} ({percentage:.1f}%)")
    
    if percentage == 100:
        print("\n🎉 DASHBOARD PERFORMANCE EXCELLENT!")
        print("✅ Watchdog installed and working")
        print("✅ File monitoring optimized")
        print("✅ Dashboard responsive")
        print("\n🚀 BENEFITS OF WATCHDOG:")
        print("   📈 Faster file change detection")
        print("   ⚡ Reduced CPU usage for file monitoring")
        print("   🔄 More efficient dashboard updates")
        print("   📊 Better real-time metric display")
        print("\n🌐 Dashboard optimized at http://localhost:8503")
        return 0
    elif percentage >= 66:
        print("\n✅ DASHBOARD PERFORMANCE GOOD!")
        print("⚠️ Some optimizations may not be fully active")
        return 0
    else:
        print("\n⚠️ DASHBOARD PERFORMANCE NEEDS IMPROVEMENT!")
        print("❌ Some performance issues detected")
        return 1

if __name__ == "__main__":
    exit(main())
