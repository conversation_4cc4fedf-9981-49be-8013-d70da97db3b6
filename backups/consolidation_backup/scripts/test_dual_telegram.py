#!/usr/bin/env python3
"""
Test Dual Telegram Notification System
Tests sending trade alerts to both primary and secondary Telegram chats.
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_dual_telegram_system():
    """Test the dual Telegram notification system."""
    print("🧪 TESTING DUAL TELEGRAM NOTIFICATION SYSTEM")
    print("=" * 60)

    try:
        # Test 1: Import and initialize enhanced wrapper
        print("🔧 Test 1: Initializing Enhanced Telegram Wrapper...")
        from core.notifications.enhanced_telegram_wrapper import EnhancedTelegramWrapper

        wrapper = EnhancedTelegramWrapper()

        if not wrapper.enabled:
            print("❌ Telegram not configured - check environment variables")
            return False

        print(f"✅ Enhanced wrapper initialized")
        print(f"  Primary chat: {wrapper.primary_chat_id}")
        print(f"  Secondary chat: {wrapper.secondary_chat_id}")
        print()

        # Test 2: Test connection to both chats
        print("🔧 Test 2: Testing connection to both chats...")
        connection_success = await wrapper.test_connection()

        if connection_success:
            print("  ✅ At least one chat connection successful")
        else:
            print("  ❌ No chat connections successful")
            return False

        print()

        # Test 3: Send sample trade notification to both chats
        print("🔧 Test 3: Sending sample trade notification to both chats...")

        sample_trade_data = {
            'signal': {
                'action': 'BUY',
                'token': 'SOL-USDC',
                'price': 180.45,
                'size': 0.001234,
                'confidence': 0.85,
                'source': 'dual_telegram_test'
            },
            'position_data': {
                'position_size_sol': 0.001234,
                'position_size_usd': 0.22,
                'total_wallet_sol': 3.096893
            },
            'transaction_result': {
                'signature': 'test_signature_12345abcdef',
                'execution_time': 1.23,
                'success': True
            }
        }

        # Set session start balance for PnL calculation
        wrapper.set_session_start_balance(3.100263)

        trade_success = await wrapper.notify_trade_executed(sample_trade_data)

        if trade_success:
            print("✅ Trade notification sent to both chats successfully!")
        else:
            print("⚠️ Trade notification had issues (check individual chat results)")

        print()

        # Test 4: Send profitability analysis to primary chat only
        print("🔧 Test 4: Sending profitability analysis to primary chat only...")

        profitability_message = """
📊 *PROFITABILITY ANALYSIS TEST*

⚠️ Status: NEAR BREAK-EVEN
📈 Current ROI: -0.109%
💰 Balance Change: -0.003370 SOL ($-0.61)

🎯 This message should only appear in the PRIMARY chat
🔒 Trade alerts go to BOTH chats
📊 Analysis goes to PRIMARY chat only

*Time*: """ + datetime.now().strftime('%H:%M:%S')

        analysis_success = await wrapper.notify_profitability_analysis(profitability_message)

        if analysis_success:
            print("✅ Profitability analysis sent to primary chat only!")
        else:
            print("❌ Failed to send profitability analysis")

        print()

        # Test 5: Send PnL milestone to both chats
        print("🔧 Test 5: Sending PnL milestone to both chats...")

        pnl_metrics = {
            'pnl_sol': -0.003370,
            'pnl_usd': -0.61,
            'pnl_percent': -0.109,
            'current_balance': 3.096893,
            'start_balance': 3.100263
        }

        pnl_success = await wrapper.notify_pnl_milestone(pnl_metrics, "test")

        if pnl_success:
            print("✅ PnL milestone sent to both chats successfully!")
        else:
            print("❌ Failed to send PnL milestone")

        print()

        # Test 6: Send system status to both chats
        print("🔧 Test 6: Sending system status to both chats...")

        status_success = await wrapper.notify_system_status(
            "testing",
            "Dual Telegram system test completed successfully"
        )

        if status_success:
            print("✅ System status sent to both chats successfully!")
        else:
            print("❌ Failed to send system status")

        print()

        # Test summary
        print("📊 TEST SUMMARY")
        print("-" * 30)

        tests = [
            ("Wrapper Initialization", wrapper.enabled),
            ("Chat Connections", connection_success),
            ("Trade Notification (Both Chats)", trade_success),
            ("Profitability Analysis (Primary Only)", analysis_success),
            ("PnL Milestone (Both Chats)", pnl_success),
            ("System Status (Both Chats)", status_success)
        ]

        passed = 0
        for test_name, result in tests:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {status} {test_name}")
            if result:
                passed += 1

        print()
        print(f"📈 Overall Result: {passed}/{len(tests)} tests passed")

        if passed == len(tests):
            print("🎉 ALL TESTS PASSED! Dual Telegram system is working perfectly!")
            print()
            print("📱 CONFIGURATION CONFIRMED:")
            print(f"  ✅ Trade alerts → Both chats ({wrapper.primary_chat_id} + {wrapper.secondary_chat_id})")
            print(f"  ✅ Profitability analysis → Primary chat only ({wrapper.primary_chat_id})")
            print(f"  ✅ System alerts → Both chats")
            return True
        else:
            print("⚠️ Some tests failed - check configuration")

        # Cleanup
        await wrapper.close()
        return passed == len(tests)

    except Exception as e:
        logger.error(f"Error during dual Telegram testing: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

async def test_individual_notifiers():
    """Test individual notifier components."""
    print("\n🔧 TESTING INDIVIDUAL NOTIFIER COMPONENTS")
    print("=" * 50)

    try:
        # Test dual notifier directly
        print("📱 Testing DualTelegramNotifier...")
        from core.notifications.dual_telegram_notifier import DualTelegramNotifier

        dual_notifier = DualTelegramNotifier()

        if dual_notifier.enabled:
            test_message = "🧪 Direct dual notifier test - this should appear in BOTH chats"
            results = await dual_notifier.send_message_dual(test_message)

            for chat_type, success in results.items():
                status = "✅" if success else "❌"
                print(f"  {status} {chat_type.capitalize()} chat: {'Success' if success else 'Failed'}")
        else:
            print("❌ Dual notifier not enabled")

        await dual_notifier.close()

        print()

        # Test original notifier
        print("📱 Testing Original TelegramNotifier...")
        from core.notifications.telegram_notifier import TelegramNotifier

        original_notifier = TelegramNotifier()

        if original_notifier.enabled:
            test_message = "🧪 Original notifier test - this should appear in PRIMARY chat only"
            success = await original_notifier.send_message(test_message)

            status = "✅" if success else "❌"
            print(f"  {status} Original notifier: {'Success' if success else 'Failed'}")
        else:
            print("❌ Original notifier not enabled")

        await original_notifier.close()

        return True

    except Exception as e:
        logger.error(f"Error testing individual notifiers: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 DUAL TELEGRAM NOTIFICATION SYSTEM TEST")
    print("=" * 60)
    print("This test will send messages to verify dual chat functionality:")
    print("  📱 Primary chat: All notifications")
    print("  📱 Secondary chat: Trade alerts only")
    print("=" * 60)
    print()

    # Test main dual system
    main_test_success = await test_dual_telegram_system()

    # Test individual components
    component_test_success = await test_individual_notifiers()

    print("\n" + "=" * 60)
    if main_test_success and component_test_success:
        print("🎉 ALL DUAL TELEGRAM TESTS COMPLETED SUCCESSFULLY!")
        print("✅ Your live trading system will now send trade alerts to BOTH chats")
        print("✅ Profitability analysis will go to PRIMARY chat only")
        return 0
    else:
        print("❌ Some tests failed - check configuration and try again")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
