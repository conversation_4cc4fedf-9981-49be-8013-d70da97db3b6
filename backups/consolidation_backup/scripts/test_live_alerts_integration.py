#!/usr/bin/env python3
"""
Live Alerts Integration Test

Tests alert system integration with live trading data and dashboard.
"""

import asyncio
import os
import sys
import json
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.notifications.telegram_notifier import TelegramNotifier

async def test_alerts_with_live_data():
    """Test alerts using actual live trading data."""
    print("🚀 Testing Alerts with Live Trading Data")
    print("=" * 50)

    notifier = TelegramNotifier()

    if not notifier.enabled:
        print("❌ Telegram not configured!")
        return False

    try:
        # Load actual live trading data
        live_data_paths = [
            "output/enhanced_live_trading/trades/",
            "phase_4_deployment/output/enhanced_live_trading/trades/"
        ]

        trades = []
        for path in live_data_paths:
            if os.path.exists(path):
                trade_files = [f for f in os.listdir(path) if f.startswith('trade_') and f.endswith('.json')]
                for trade_file in sorted(trade_files)[-3:]:  # Last 3 trades
                    with open(os.path.join(path, trade_file), 'r') as f:
                        trade_data = json.load(f)
                        trades.append(trade_data)
                break

        if not trades:
            print("⚠️ No live trading data found - using test data")
            # Generate test trade
            trades = [{
                'signal': {
                    'action': 'BUY',
                    'size': 0.025,
                    'confidence': 0.89,
                    'price': 181.75,
                    'strategy': 'Live Test'
                },
                'position_data': {
                    'position_size_sol': 0.025,
                    'position_size_usd': 4.54,
                    'total_wallet_sol': 3.950000000
                },
                'transaction_result': {
                    'success': True,
                    'signature': '4xdGrUpKJi6EbPL2mNxqVQKjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHj',
                    'execution_time': 0.156
                },
                'timestamp': datetime.now().isoformat()
            }]

        print(f"✅ Found {len(trades)} trades to test with")

        # Set session start balance
        notifier.set_session_start_balance(3.947251432)

        # Test alerts with each trade
        for i, trade in enumerate(trades):
            print(f"🔄 Testing alert for trade {i+1}...")

            success = await notifier.notify_trade_executed(trade)
            if success:
                print(f"✅ Trade {i+1} alert sent successfully!")
            else:
                print(f"❌ Trade {i+1} alert failed!")
                return False

            await asyncio.sleep(1)  # Rate limiting

        # Test session summary with real metrics
        print("🔄 Testing session summary with live data...")

        # Load metrics
        metrics_paths = [
            "output/enhanced_live_trading/latest_metrics.json",
            "phase_4_deployment/output/enhanced_live_trading/latest_metrics.json"
        ]

        metrics = None
        for path in metrics_paths:
            if os.path.exists(path):
                with open(path, 'r') as f:
                    metrics = json.load(f)
                break

        if not metrics:
            metrics = {
                'cycles_completed': 20,
                'trades_executed': len(trades),
                'trades_rejected': 1,
                'session_duration_minutes': 35.5
            }

        success = await notifier.notify_session_ended(
            metrics,
            final_balance=3.952251432,
            avg_price=181.75
        )

        if success:
            print("✅ Session summary alert sent successfully!")
        else:
            print("❌ Session summary alert failed!")
            return False

        await notifier.close()
        return True

    except Exception as e:
        print(f"❌ Error during live data testing: {e}")
        return False

async def test_dashboard_alert_integration():
    """Test alert integration with dashboard data."""
    print("\n📊 Testing Dashboard Alert Integration")
    print("=" * 50)

    try:
        # Import dashboard data service
        sys.path.append('phase_4_deployment/unified_dashboard')
        from phase_4_deployment.unified_dashboard.data_service import DataService

        # Load dashboard data
        data_service = DataService()
        dashboard_data = data_service.load_all_data()

        print("✅ Dashboard data loaded successfully")

        # Check live trading data in dashboard
        live_trading = dashboard_data.get('live_trading', {})
        real_time_metrics = dashboard_data.get('real_time_metrics', {})

        if live_trading.get('trades'):
            print(f"✅ Dashboard has {len(live_trading['trades'])} live trades")
        else:
            print("⚠️ No live trades in dashboard data")

        if real_time_metrics.get('execution_stats'):
            stats = real_time_metrics['execution_stats']
            print(f"✅ Dashboard has execution stats: {stats.get('total_trades', 0)} trades")
        else:
            print("⚠️ No execution stats in dashboard data")

        # Test alert with dashboard data
        notifier = TelegramNotifier()
        if notifier.enabled and live_trading.get('trades'):
            print("🔄 Testing alert with dashboard data...")

            # Use latest trade from dashboard
            latest_trade = live_trading['trades'][-1]

            success = await notifier.notify_trade_executed(latest_trade)
            if success:
                print("✅ Dashboard data alert sent successfully!")
                return True
            else:
                print("❌ Dashboard data alert failed!")
                return False
        else:
            print("⚠️ Skipping dashboard alert test (no data or Telegram disabled)")
            return True

    except Exception as e:
        print(f"❌ Error during dashboard integration testing: {e}")
        return False

async def test_alert_performance():
    """Test alert system performance and reliability."""
    print("\n⚡ Testing Alert Performance")
    print("=" * 50)

    notifier = TelegramNotifier()

    if not notifier.enabled:
        print("⚠️ Telegram not configured - skipping performance test")
        return True

    try:
        # Test rapid alerts (rate limiting)
        print("🔄 Testing rate limiting...")

        start_time = datetime.now()

        # Send multiple alerts quickly
        results = []
        for i in range(3):
            result = await notifier.send_message(f"Performance test message {i+1}")
            results.append(result)

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        successful_alerts = sum(results)
        print(f"📊 Sent {successful_alerts}/3 alerts in {duration:.2f} seconds")

        if successful_alerts >= 2:  # At least 2/3 should succeed
            print("✅ Performance test passed!")
        else:
            print("❌ Performance test failed!")
            return False

        # Test error handling
        print("🔄 Testing error handling...")

        # Test with invalid data
        invalid_trade = {
            'signal': None,  # Invalid data
            'position_data': {},
            'transaction_result': {}
        }

        # This should not crash
        try:
            await notifier.notify_trade_executed(invalid_trade)
            print("✅ Error handling test passed!")
        except Exception as e:
            print(f"❌ Error handling test failed: {e}")
            return False

        await notifier.close()
        return True

    except Exception as e:
        print(f"❌ Error during performance testing: {e}")
        return False

async def test_pnl_tracking_accuracy():
    """Test PnL tracking accuracy in alerts."""
    print("\n💰 Testing PnL Tracking Accuracy")
    print("=" * 50)

    notifier = TelegramNotifier()

    if not notifier.enabled:
        print("⚠️ Telegram not configured - skipping PnL test")
        return True

    try:
        # Set known starting balance
        start_balance = 3.947251432
        notifier.set_session_start_balance(start_balance)
        print(f"✅ Set starting balance: {start_balance:.6f} SOL")

        # Simulate trades with known outcomes
        trades = [
            {'balance_after': 3.950000000, 'price': 180.0, 'action': 'BUY'},
            {'balance_after': 3.955000000, 'price': 182.0, 'action': 'SELL'},
            {'balance_after': 3.952000000, 'price': 181.0, 'action': 'BUY'}
        ]

        for i, trade_sim in enumerate(trades):
            # Calculate expected PnL
            expected_pnl_sol = trade_sim['balance_after'] - start_balance
            expected_pnl_usd = expected_pnl_sol * trade_sim['price']

            # Test PnL calculation
            pnl_metrics = notifier.calculate_session_pnl(
                trade_sim['balance_after'],
                trade_sim['price']
            )

            # Verify accuracy
            if abs(pnl_metrics['pnl_sol'] - expected_pnl_sol) < 0.000001:
                print(f"✅ Trade {i+1} PnL calculation accurate: {pnl_metrics['pnl_sol']:+.6f} SOL")
            else:
                print(f"❌ Trade {i+1} PnL calculation error!")
                return False

        # Test milestone detection
        print("🔄 Testing PnL milestone detection...")

        # Simulate profit milestone
        profit_metrics = {
            'pnl_sol': 0.005,
            'pnl_usd': 0.90,
            'pnl_percent': 0.13,
            'current_balance': 3.952251432,
            'start_balance': start_balance
        }

        success = await notifier.notify_pnl_milestone(profit_metrics, "profit")
        if success:
            print("✅ PnL milestone alert sent successfully!")
        else:
            print("❌ PnL milestone alert failed!")
            return False

        await notifier.close()
        return True

    except Exception as e:
        print(f"❌ Error during PnL testing: {e}")
        return False

async def main():
    """Main test function."""
    print("🎯 LIVE ALERTS INTEGRATION TEST")
    print("=" * 60)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Test results
    results = {}

    # Test 1: Alerts with Live Data
    results['live_data'] = await test_alerts_with_live_data()

    # Test 2: Dashboard Integration
    results['dashboard'] = await test_dashboard_alert_integration()

    # Test 3: Performance Testing
    results['performance'] = await test_alert_performance()

    # Test 4: PnL Tracking
    results['pnl_tracking'] = await test_pnl_tracking_accuracy()

    # Summary
    print("\n" + "=" * 60)
    print("📊 LIVE ALERTS INTEGRATION SUMMARY")
    print("=" * 60)

    passed_tests = sum(results.values())
    total_tests = len(results)

    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title()}: {status}")

    print(f"\n🎯 Overall Score: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")

    if passed_tests == total_tests:
        print("\n🎉 LIVE ALERTS INTEGRATION PERFECT!")
        print("🚀 Alert system fully integrated with live trading and dashboard!")
        return 0
    elif passed_tests >= total_tests * 0.75:
        print("\n✅ LIVE ALERTS INTEGRATION EXCELLENT!")
        print("⚠️ Minor issues detected - system ready for production")
        return 0
    else:
        print("\n⚠️ LIVE ALERTS INTEGRATION NEEDS WORK!")
        print("❌ Multiple failures detected - please fix before live trading")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
