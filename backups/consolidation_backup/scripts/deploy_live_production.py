#!/usr/bin/env python3
"""
Live Production Deployment Script
Implements the complete 0.5 wallet strategy deployment plan.
"""

import asyncio
import logging
import json
import yaml
import os
import sys
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionDeployment:
    """Handles the complete deployment of live production trading."""

    def __init__(self):
        """Initialize the deployment manager."""
        self.deployment_start = datetime.now()
        self.config_path = "config/live_production.yaml"
        self.wallet_balance = 0.0
        self.active_capital = 0.0

        logger.info("🚀 Production Deployment Manager initialized")

    def validate_environment(self) -> bool:
        """Validate that the environment is ready for production deployment."""
        logger.info("📋 Validating production environment...")

        checks = []

        # Check environment variables
        required_env_vars = [
            'HELIUS_RPC_URL', 'HELIUS_API_KEY', 'BIRDEYE_API_KEY',
            'WALLET_ADDRESS', 'KEYPAIR_PATH', 'TELEGRAM_BOT_TOKEN', 'TELEGRAM_CHAT_ID'
        ]

        for var in required_env_vars:
            if os.getenv(var):
                checks.append(f"✅ {var} configured")
            else:
                checks.append(f"❌ {var} missing")
                logger.error(f"Missing environment variable: {var}")

        # Check configuration files
        config_files = [
            'config/live_production.yaml',
            '.env'
        ]

        for config_file in config_files:
            if os.path.exists(config_file):
                checks.append(f"✅ {config_file} exists")
            else:
                checks.append(f"❌ {config_file} missing")
                logger.error(f"Missing configuration file: {config_file}")

        # Check wallet keypair
        keypair_path = os.getenv('KEYPAIR_PATH', '')
        if keypair_path and os.path.exists(keypair_path):
            checks.append(f"✅ Wallet keypair found")
        else:
            checks.append(f"❌ Wallet keypair missing")
            logger.error(f"Wallet keypair not found at: {keypair_path}")

        # Check output directories
        output_dirs = [
            'output/live_production/cycles',
            'output/live_production/dashboard',
            'output/wallet',
            'logs'
        ]

        for output_dir in output_dirs:
            os.makedirs(output_dir, exist_ok=True)
            checks.append(f"✅ {output_dir} ready")

        # Print validation results
        logger.info("Environment validation results:")
        for check in checks:
            logger.info(f"  {check}")

        # Return True if all critical checks pass
        failed_checks = [check for check in checks if check.startswith('❌')]
        if failed_checks:
            logger.error(f"Environment validation failed: {len(failed_checks)} issues found")
            return False

        logger.info("✅ Environment validation passed")
        return True

    def load_wallet_balance(self) -> bool:
        """Load current wallet balance and calculate active capital."""
        try:
            wallet_file = "output/wallet/wallet_balance.json"
            if os.path.exists(wallet_file):
                with open(wallet_file, 'r') as f:
                    wallet_data = json.load(f)

                self.wallet_balance = wallet_data.get('wallet_balance', {}).get('trading_wallet', 0.0)
                self.active_capital = self.wallet_balance * 0.5  # 0.5 strategy

                logger.info(f"💰 Wallet Balance: {self.wallet_balance:.4f} SOL")
                logger.info(f"💰 Active Capital: {self.active_capital:.4f} SOL (50% strategy)")
                logger.info(f"💰 Reserve Balance: {self.wallet_balance - self.active_capital:.4f} SOL")

                return True
            else:
                logger.error("Wallet balance file not found")
                return False

        except Exception as e:
            logger.error(f"Error loading wallet balance: {e}")
            return False

    def update_configuration(self) -> bool:
        """Update configuration for production deployment."""
        logger.info("⚙️ Updating configuration for production...")

        try:
            # Load existing config
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    config = yaml.safe_load(f)
            else:
                logger.error(f"Configuration file not found: {self.config_path}")
                return False

            # Update wallet-specific settings based on actual balance
            if self.wallet_balance > 0:
                # Calculate position sizes based on active capital
                min_position_usd = 20  # Minimum for fee efficiency
                target_position_usd = 40  # Target position size
                sol_price = 180  # Default SOL price

                min_position_sol = min_position_usd / sol_price
                target_position_sol = target_position_usd / sol_price

                # Update trading configuration
                config['trading']['min_trade_size_usd'] = min_position_usd
                config['trading']['target_trade_size_usd'] = target_position_usd
                config['trading']['base_position_size_pct'] = target_position_sol / self.active_capital

                # Ensure position sizes are reasonable
                config['trading']['base_position_size_pct'] = min(0.1, max(0.02, config['trading']['base_position_size_pct']))

                logger.info(f"📊 Updated position sizing for {self.active_capital:.4f} SOL active capital")
                logger.info(f"📊 Base position size: {config['trading']['base_position_size_pct']:.1%} of active capital")

            # Save updated configuration
            with open(self.config_path, 'w') as f:
                yaml.dump(config, f, default_flow_style=False, indent=2)

            logger.info("✅ Configuration updated successfully")
            return True

        except Exception as e:
            logger.error(f"Error updating configuration: {e}")
            return False

    def run_system_validation(self) -> bool:
        """Run system validation tests."""
        logger.info("🧪 Running system validation tests...")

        try:
            # Run the enhanced trade analysis to validate current state
            logger.info("Running trade analysis validation...")
            result = subprocess.run([
                sys.executable, 'scripts/analyze_trades.py', '--quiet'
            ], capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                logger.info("✅ Trade analysis validation passed")
            else:
                logger.warning(f"Trade analysis validation issues: {result.stderr}")

            # Test position sizer
            logger.info("Testing production position sizer...")
            try:
                # Add current directory to Python path
                sys.path.insert(0, os.getcwd())

                from core.risk.production_position_sizer import create_production_position_sizer

                sizer = create_production_position_sizer(self.config_path)
                sizer.update_wallet_state(self.wallet_balance, 0.0, 180.0)

                # Test position calculation
                test_position = sizer.calculate_position_size(
                    signal_strength=0.8,
                    strategy='mean_reversion',
                    market_regime='ranging',
                    volatility=0.3
                )

                if not test_position.get('rejected', True):
                    logger.info(f"✅ Position sizer test passed: {test_position['position_size_usd']:.2f} USD position")
                else:
                    logger.warning(f"Position sizer test issues: {test_position.get('rejection_reason', 'Unknown')}")

            except Exception as e:
                logger.error(f"Position sizer test failed: {e}")
                return False

            logger.info("✅ System validation completed")
            return True

        except Exception as e:
            logger.error(f"System validation failed: {e}")
            return False

    def start_dashboard(self) -> bool:
        """Start the production dashboard."""
        logger.info("📊 Starting production dashboard...")

        try:
            # Check if dashboard script exists
            dashboard_script = "scripts/update_dashboard_for_production.py"
            if not os.path.exists(dashboard_script):
                logger.warning("Dashboard script not found, skipping dashboard startup")
                return True

            logger.info("Dashboard script available for manual startup")
            logger.info(f"Run: streamlit run {dashboard_script}")

            return True

        except Exception as e:
            logger.error(f"Error starting dashboard: {e}")
            return False

    async def deploy_live_trading(self, test_duration_hours: float = 1.0) -> bool:
        """Deploy and start live trading."""
        logger.info(f"🚀 Deploying live trading for {test_duration_hours} hours...")

        try:
            # Use the existing live trading system with production configuration
            from phase_4_deployment.start_live_trading import run_live_trading

            logger.info("Starting live production trading with existing system...")

            # Set environment variables for production mode
            os.environ['TRADING_ENABLED'] = 'true'
            os.environ['PAPER_TRADING'] = 'false'
            os.environ['DRY_RUN'] = 'false'

            # Run for the specified duration
            import asyncio

            async def run_with_timeout():
                try:
                    # Create a task for the trading system
                    trading_task = asyncio.create_task(run_live_trading())

                    # Wait for the specified duration
                    await asyncio.sleep(test_duration_hours * 3600)

                    # Cancel the trading task
                    trading_task.cancel()

                    try:
                        await trading_task
                    except asyncio.CancelledError:
                        logger.info("Trading session completed successfully")

                    return True

                except Exception as e:
                    logger.error(f"Error in trading session: {e}")
                    return False

            success = await run_with_timeout()

            if success:
                logger.info("✅ Live trading deployment successful")
                return True
            else:
                logger.error("❌ Live trading deployment failed")
                return False

        except Exception as e:
            logger.error(f"Error deploying live trading: {e}")
            return False

    def generate_deployment_report(self, success: bool):
        """Generate deployment report."""
        logger.info("📋 Generating deployment report...")

        deployment_duration = datetime.now() - self.deployment_start

        report = {
            'deployment_timestamp': self.deployment_start.isoformat(),
            'deployment_duration_minutes': deployment_duration.total_seconds() / 60,
            'deployment_success': success,
            'wallet_balance_sol': self.wallet_balance,
            'active_capital_sol': self.active_capital,
            'reserve_balance_sol': self.wallet_balance - self.active_capital,
            'strategy': '0.5_wallet_strategy',
            'configuration_file': self.config_path,
            'environment_validated': True,
            'system_validated': True,
            'dashboard_available': True
        }

        # Save report
        report_file = f"output/live_production/deployment_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        logger.info(f"📋 Deployment report saved: {report_file}")

        # Print summary
        logger.info("🎯 DEPLOYMENT SUMMARY")
        logger.info(f"   Status: {'✅ SUCCESS' if success else '❌ FAILED'}")
        logger.info(f"   Duration: {deployment_duration.total_seconds()/60:.1f} minutes")
        logger.info(f"   Wallet Balance: {self.wallet_balance:.4f} SOL")
        logger.info(f"   Active Capital: {self.active_capital:.4f} SOL")
        logger.info(f"   Strategy: 0.5 Wallet Optimization")

        return report_file

    async def run_full_deployment(self, test_duration_hours: float = 1.0) -> bool:
        """Run the complete production deployment process."""
        logger.info("🚀 STARTING LIVE PRODUCTION DEPLOYMENT")
        logger.info("=" * 60)

        try:
            # Phase 1: Environment validation
            if not self.validate_environment():
                logger.error("❌ Environment validation failed")
                return False

            # Phase 2: Load wallet data
            if not self.load_wallet_balance():
                logger.error("❌ Failed to load wallet balance")
                return False

            # Phase 3: Update configuration
            if not self.update_configuration():
                logger.error("❌ Configuration update failed")
                return False

            # Phase 4: System validation
            if not self.run_system_validation():
                logger.error("❌ System validation failed")
                return False

            # Phase 5: Start dashboard
            if not self.start_dashboard():
                logger.warning("⚠️ Dashboard startup issues (non-critical)")

            # Phase 6: Deploy live trading
            success = await self.deploy_live_trading(test_duration_hours)

            # Phase 7: Generate report
            self.generate_deployment_report(success)

            if success:
                logger.info("🎉 LIVE PRODUCTION DEPLOYMENT SUCCESSFUL!")
                logger.info("💰 Trading with real assets using 0.5 wallet strategy")
                logger.info("📊 Monitor progress via dashboard and logs")
            else:
                logger.error("💥 LIVE PRODUCTION DEPLOYMENT FAILED!")

            return success

        except Exception as e:
            logger.error(f"Deployment error: {e}")
            self.generate_deployment_report(False)
            return False


async def main():
    """Main deployment function."""
    import argparse

    parser = argparse.ArgumentParser(description="Deploy Live Production Trading")
    parser.add_argument("--test-duration", type=float, default=1.0,
                       help="Test duration in hours (default: 1.0)")
    parser.add_argument("--validate-only", action="store_true",
                       help="Only run validation, don't start trading")

    args = parser.parse_args()

    # Create deployment manager
    deployment = ProductionDeployment()

    if args.validate_only:
        # Run validation only
        logger.info("Running validation only...")
        env_ok = deployment.validate_environment()
        wallet_ok = deployment.load_wallet_balance()
        config_ok = deployment.update_configuration()
        system_ok = deployment.run_system_validation()

        if all([env_ok, wallet_ok, config_ok, system_ok]):
            logger.info("✅ All validations passed - ready for deployment")
            return 0
        else:
            logger.error("❌ Validation failed - fix issues before deployment")
            return 1
    else:
        # Run full deployment
        success = await deployment.run_full_deployment(args.test_duration)
        return 0 if success else 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
