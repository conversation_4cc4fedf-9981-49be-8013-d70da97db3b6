#!/usr/bin/env python3
"""
Test script to validate the Orca "Invalid price: 0" fix.
This script tests the new Jupiter-based implementation.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.dex.orca_swap_builder import OrcaSwapBuilder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_price_fetching():
    """Test real-time price fetching functionality."""
    logger.info("🧪 Testing price fetching...")
    
    # Get wallet address from environment
    wallet_address = os.environ.get('WALLET_ADDRESS')
    if not wallet_address:
        logger.error("❌ WALLET_ADDRESS not found in environment")
        return False
    
    # Initialize swap builder
    swap_builder = OrcaSwapBuilder(wallet_address)
    
    try:
        # Initialize without requiring keypair for price testing
        await swap_builder.initialize()
        
        # Test price fetching for SOL-USDC
        logger.info("📊 Testing SOL price fetching...")
        sol_price = await swap_builder._get_current_price('SOL', 'USDC')
        
        if sol_price > 0:
            logger.info(f"✅ SOL price fetched successfully: ${sol_price}")
            return True
        else:
            logger.error("❌ Failed to fetch SOL price")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in price fetching test: {e}")
        return False
    finally:
        await swap_builder.close()

async def test_jupiter_quote():
    """Test Jupiter quote functionality."""
    logger.info("🧪 Testing Jupiter quote...")
    
    # Get wallet address from environment
    wallet_address = os.environ.get('WALLET_ADDRESS')
    if not wallet_address:
        logger.error("❌ WALLET_ADDRESS not found in environment")
        return False
    
    # Initialize swap builder
    swap_builder = OrcaSwapBuilder(wallet_address)
    
    try:
        await swap_builder.initialize()
        
        # Test Jupiter quote for small SOL->USDC swap
        sol_mint = "So11111111111111111111111111111111111111112"
        usdc_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        amount = 1000000  # 0.001 SOL
        
        logger.info(f"📊 Testing Jupiter quote for {amount} lamports SOL->USDC...")
        quote = await swap_builder._get_jupiter_quote(sol_mint, usdc_mint, amount)
        
        if quote and 'outAmount' in quote:
            logger.info(f"✅ Jupiter quote received: {quote['outAmount']} USDC")
            logger.info(f"📈 Price impact: {quote.get('priceImpactPct', 'N/A')}%")
            return True
        else:
            logger.error("❌ Failed to get Jupiter quote")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in Jupiter quote test: {e}")
        return False
    finally:
        await swap_builder.close()

async def test_signal_with_zero_price():
    """Test signal processing with zero price (should fetch real-time price)."""
    logger.info("🧪 Testing signal with zero price...")
    
    # Get wallet address from environment
    wallet_address = os.environ.get('WALLET_ADDRESS')
    if not wallet_address:
        logger.error("❌ WALLET_ADDRESS not found in environment")
        return False
    
    # Initialize swap builder
    swap_builder = OrcaSwapBuilder(wallet_address)
    
    try:
        await swap_builder.initialize()
        
        # Create test signal with zero price
        test_signal = {
            'action': 'BUY',
            'market': 'SOL-USDC',
            'size': 0.001,  # Small test size
            'price': 0,     # Zero price - should trigger real-time fetch
            'confidence': 0.8,
            'timestamp': '2025-01-27T12:00:00'
        }
        
        logger.info("📊 Testing signal processing with zero price...")
        result = await swap_builder.build_swap_transaction(test_signal)
        
        if result and result.get('success'):
            logger.info("✅ Signal processed successfully with real-time price")
            logger.info(f"💰 Market price used: ${result.get('market_price', 'N/A')}")
            logger.info(f"🔄 Execution type: {result.get('execution_type', 'N/A')}")
            return True
        else:
            logger.error(f"❌ Signal processing failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in signal test: {e}")
        return False
    finally:
        await swap_builder.close()

async def test_signal_validation():
    """Test signal validation with the updated logic."""
    logger.info("🧪 Testing signal validation...")
    
    # Get wallet address from environment
    wallet_address = os.environ.get('WALLET_ADDRESS')
    if not wallet_address:
        logger.error("❌ WALLET_ADDRESS not found in environment")
        return False
    
    # Initialize swap builder
    swap_builder = OrcaSwapBuilder(wallet_address)
    
    try:
        await swap_builder.initialize()
        
        # Test valid signal with zero price (should pass validation)
        valid_signal = {
            'action': 'BUY',
            'market': 'SOL-USDC',
            'size': 0.001,
            'price': 0,  # Zero price should now be valid
            'confidence': 0.8
        }
        
        logger.info("📊 Testing validation of signal with zero price...")
        is_valid = await swap_builder.validate_swap_parameters(valid_signal)
        
        if is_valid:
            logger.info("✅ Signal validation passed for zero price")
        else:
            logger.error("❌ Signal validation failed for zero price")
            return False
        
        # Test invalid signal with negative price
        invalid_signal = {
            'action': 'BUY',
            'market': 'SOL-USDC',
            'size': 0.001,
            'price': -1,  # Negative price should fail
            'confidence': 0.8
        }
        
        logger.info("📊 Testing validation of signal with negative price...")
        is_valid = await swap_builder.validate_swap_parameters(invalid_signal)
        
        if not is_valid:
            logger.info("✅ Signal validation correctly rejected negative price")
            return True
        else:
            logger.error("❌ Signal validation incorrectly accepted negative price")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in validation test: {e}")
        return False
    finally:
        await swap_builder.close()

async def main():
    """Run all tests."""
    logger.info("🚀 Starting Orca price fix validation tests...")
    
    tests = [
        ("Price Fetching", test_price_fetching),
        ("Jupiter Quote", test_jupiter_quote),
        ("Signal with Zero Price", test_signal_with_zero_price),
        ("Signal Validation", test_signal_validation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("📊 TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The Orca price fix is working correctly.")
        return True
    else:
        logger.error("💥 Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
