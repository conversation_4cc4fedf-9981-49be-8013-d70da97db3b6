#!/usr/bin/env python3
"""
Create Focused Deprecation List

Creates a focused list of files to deprecate that could interfere with our working system.
"""

import os
from datetime import datetime

def create_focused_depr_list():
    """Create a focused deprecation list."""
    
    # Critical conflicting files that could interfere with our working system
    critical_conflicts = [
        # Conflicting configuration files
        "logging_config.json",
        "config/whale_config.yaml", 
        "config/schemas/config_schema.json",
        "phase_4_deployment/carbon_core_config.yaml",
        "phase_4_deployment/production_config.yaml",
        "phase_4_deployment/configs/carbon_core_config.yaml",
        "phase_4_deployment/configs/helius_config.yaml",
        "phase_4_deployment/configs/jito_config.yaml",
        
        # Outdated root files that could cause confusion
        ".env.example",
        ".env.paper", 
        ".env.production",
        ".env.simulation",
        "logging_config.json",
        "pytest.ini",
        "requirements-test.txt",
        "requirements.txt.backup",
        "setup_fallback.py",
        "simple_paper_trading_monitor.py",
        "simulation_results.json",
        "validation_report.json",
        
        # Outdated test files in root
        "test_complete_integration.py",
        "test_phase1_integration.py", 
        "test_phase2_simple.py",
        "test_phase3_attribution.py",
        
        # Conflicting directories that could cause import issues
        "backups/",
        "configs/",
        "utils/",
        "tests/unit/",
        "tests/core/",
        "tests/integration/",
        "tests/performance/",
        "tests/e2e/",
        "tests/functional/",
        "tests/shared/",
        "tests/utils/",
        
        # Documentation files that are outdated
        "BUSINESS_RISK_ASSESSMENT.md",
        "COMPLETE_INTEGRATION_SUMMARY.md", 
        "DEPLOYMENT_COMPLETE.md",
        "EMERGENCY_PROCEDURES.md",
        "ENHANCED_SYSTEM_SUMMARY.md",
        "ENHANCED_TRADING_SYSTEM.md",
        "ENTRY_POINTS.md",
        "FINAL_TEST_SYSTEM_DEPLOYMENT_ALIGNMENT.md",
        "LIVE_PRODUCTION_STRATEGY_PLAN.md",
        "LIVE_PRODUCTION_SUCCESS_REPORT.md",
        "LIVE_PRODUCTION_TEST_RESULTS.md",
        "LIVE_TRADING_ENTRY_POINTS_ALIGNED.md",
        "NEXT_ACTIONS_COMPLETED.md",
        "PHASE1_IMPLEMENTATION_SUMMARY.md",
        "PHASE2_IMPLEMENTATION_SUMMARY.md", 
        "PHASE4_IMPLEMENTATION_SUMMARY.md",
        "POSITION_FLATTENING_SOLUTION.md",
        "PRODUCTION_CONFIGURATION_SUMMARY.md",
        "RL_LEARNING_SYSTEM.md",
        "SYSTEM_DOCUMENTATION_UPDATE.md",
        "SYSTEM_TEST_SUMMARY.md",
        "Synergy7.md",
        "TRANSACTION_FIXES_COMPLETE.md",
        "UPDATED_TEST_SYSTEM_SUMMARY.md",
        "WALLET_CONFIGURATION_STATUS.md",
        "WALLET_SETUP_COMPLETE.md",
        "WHALE_RL_IMPLEMENTATION_PLAN.md",
    ]
    
    # Check which files actually exist
    existing_conflicts = []
    for file_path in critical_conflicts:
        if os.path.exists(file_path):
            existing_conflicts.append(file_path)
    
    return existing_conflicts

def update_depr_txt():
    """Update depr.txt with focused deprecation list."""
    
    existing_conflicts = create_focused_depr_list()
    
    # Read current depr.txt
    current_depr = []
    if os.path.exists("depr.txt"):
        with open("depr.txt", 'r') as f:
            current_depr = [line.strip() for line in f.readlines() if line.strip()]
    
    # Add header for new section
    new_entries = [
        f"",
        f"# System Cleanup - {datetime.now().strftime('%Y-%m-%d')}",
        f"# Files that could interfere with current working system",
        f""
    ]
    
    # Add new conflicts
    added_count = 0
    for conflict in existing_conflicts:
        entry = f"{conflict} # Potential conflict with working system"
        if entry not in current_depr:
            new_entries.append(entry)
            added_count += 1
    
    # Write updated depr.txt
    with open("depr.txt", 'a') as f:
        for entry in new_entries:
            f.write(entry + "\n")
    
    print(f"✅ Added {added_count} new entries to depr.txt")
    print(f"📄 Total conflicts identified: {len(existing_conflicts)}")
    
    return added_count

def main():
    """Main function."""
    print("🗑️ CREATING FOCUSED DEPRECATION LIST")
    print("=" * 50)
    print("Identifying files that could interfere with working system")
    print("=" * 50)
    
    # Create focused list
    conflicts = create_focused_depr_list()
    
    print("⚠️ CRITICAL CONFLICTS IDENTIFIED:")
    for conflict in conflicts[:20]:  # Show first 20
        print(f"   {conflict}")
    
    if len(conflicts) > 20:
        print(f"   ... and {len(conflicts) - 20} more")
    
    # Update depr.txt
    added = update_depr_txt()
    
    print(f"\n✅ FOCUSED CLEANUP COMPLETE")
    print(f"📊 Files identified: {len(conflicts)}")
    print(f"📝 Added to depr.txt: {added}")
    print(f"🎯 Ready for selective cleanup")
    
    return 0

if __name__ == "__main__":
    exit(main())
