#!/usr/bin/env python3
"""
Jupiter Configuration Setup Script

This script ensures proper Jupiter integration configuration for the Enhanced Trading System.
It validates settings, tests connectivity, and provides configuration recommendations.
"""

import os
import sys
import yaml
import json
import asyncio
import httpx
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('jupiter_setup')

class JupiterConfigManager:
    """Manager for Jupiter configuration setup and validation."""
    
    def __init__(self):
        self.config_path = "config/jupiter_config.yaml"
        self.environment = os.getenv("ENVIRONMENT", "production")
        self.config = None
        self.http_client = None
        
    async def initialize(self):
        """Initialize the configuration manager."""
        self.http_client = httpx.AsyncClient(timeout=30.0)
        await self.load_config()
        
    async def close(self):
        """Close HTTP client."""
        if self.http_client:
            await self.http_client.aclose()
            
    async def load_config(self):
        """Load Jupiter configuration from file."""
        try:
            if not os.path.exists(self.config_path):
                logger.error(f"Jupiter config file not found: {self.config_path}")
                return False
                
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
                
            # Apply environment-specific overrides
            if 'environments' in self.config and self.environment in self.config['environments']:
                env_config = self.config['environments'][self.environment]
                self._merge_config(self.config, env_config)
                
            logger.info(f"✅ Jupiter config loaded for environment: {self.environment}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load Jupiter config: {e}")
            return False
            
    def _merge_config(self, base_config: Dict, override_config: Dict):
        """Merge environment-specific configuration."""
        for key, value in override_config.items():
            if isinstance(value, dict) and key in base_config:
                self._merge_config(base_config[key], value)
            else:
                base_config[key] = value
                
    async def test_jupiter_connectivity(self) -> bool:
        """Test connectivity to Jupiter APIs."""
        logger.info("🔗 Testing Jupiter API connectivity...")
        
        tests = [
            ("Quote API", self.config['api']['quote_url']),
            ("Price API", self.config['api']['price_url']),
            ("Token List API", self.config['api']['tokens_url'])
        ]
        
        results = []
        
        for test_name, url in tests:
            try:
                response = await self.http_client.get(url, params={"inputMint": "So11111111111111111111111111111111111111112", "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "amount": "1000000000"} if "quote" in url else {})
                
                if response.status_code == 200:
                    logger.info(f"✅ {test_name}: Connected")
                    results.append(True)
                else:
                    logger.warning(f"⚠️ {test_name}: HTTP {response.status_code}")
                    results.append(False)
                    
            except Exception as e:
                logger.error(f"❌ {test_name}: {e}")
                results.append(False)
                
        success_rate = sum(results) / len(results) * 100
        logger.info(f"📊 Jupiter connectivity: {success_rate:.0f}% ({sum(results)}/{len(results)} tests passed)")
        
        return success_rate >= 66  # At least 2/3 tests should pass
        
    async def validate_configuration(self) -> Dict[str, Any]:
        """Validate Jupiter configuration settings."""
        logger.info("🔍 Validating Jupiter configuration...")
        
        validation_results = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'recommendations': []
        }
        
        # Validate slippage settings
        slippage_bps = self.config['transaction']['default_slippage_bps']
        max_slippage = self.config['transaction']['max_slippage_bps']
        min_slippage = self.config['transaction']['min_slippage_bps']
        
        if slippage_bps < min_slippage or slippage_bps > max_slippage:
            validation_results['errors'].append(f"Default slippage {slippage_bps} outside valid range [{min_slippage}, {max_slippage}]")
            validation_results['valid'] = False
            
        if slippage_bps > 100:  # 1%
            validation_results['warnings'].append(f"High default slippage: {slippage_bps} bps ({slippage_bps/100:.1f}%)")
            
        # Validate risk settings
        max_price_impact = self.config['risk']['max_price_impact_pct']
        if max_price_impact > 5.0:
            validation_results['warnings'].append(f"High max price impact: {max_price_impact}%")
            
        min_liquidity = self.config['risk']['min_liquidity_usd']
        if min_liquidity < 1000:
            validation_results['warnings'].append(f"Low minimum liquidity requirement: ${min_liquidity:,}")
            
        # Validate token configuration
        known_mints = self.config['tokens']['known_mints']
        required_tokens = ['SOL', 'USDC']
        
        for token in required_tokens:
            if token not in known_mints:
                validation_results['errors'].append(f"Missing required token mint: {token}")
                validation_results['valid'] = False
                
        # Environment-specific recommendations
        if self.environment == 'production':
            if slippage_bps > 75:
                validation_results['recommendations'].append("Consider lower slippage for production (≤75 bps)")
            if not self.config['circuit_breaker']['enabled']:
                validation_results['recommendations'].append("Enable circuit breaker for production")
                
        logger.info(f"📋 Configuration validation: {'✅ Valid' if validation_results['valid'] else '❌ Invalid'}")
        
        return validation_results
        
    async def test_jupiter_quote(self) -> bool:
        """Test Jupiter quote functionality with real parameters."""
        logger.info("💱 Testing Jupiter quote functionality...")
        
        try:
            # Test SOL -> USDC quote
            params = {
                "inputMint": self.config['tokens']['known_mints']['SOL'],
                "outputMint": self.config['tokens']['known_mints']['USDC'],
                "amount": "100000000",  # 0.1 SOL
                "slippageBps": str(self.config['transaction']['default_slippage_bps'])
            }
            
            response = await self.http_client.get(
                self.config['api']['quote_url'],
                params=params
            )
            
            if response.status_code == 200:
                quote_data = response.json()
                
                if 'outAmount' in quote_data and 'routePlan' in quote_data:
                    out_amount = int(quote_data['outAmount'])
                    route_plan = quote_data['routePlan']
                    
                    logger.info(f"✅ Quote test successful:")
                    logger.info(f"   Input: 0.1 SOL")
                    logger.info(f"   Output: {out_amount / 1_000_000:.2f} USDC")
                    logger.info(f"   Routes: {len(route_plan)} steps")
                    
                    return True
                else:
                    logger.error(f"❌ Invalid quote response format")
                    return False
                    
            else:
                logger.error(f"❌ Quote API error: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Quote test failed: {e}")
            return False
            
    def generate_config_summary(self) -> Dict[str, Any]:
        """Generate a summary of current Jupiter configuration."""
        return {
            'environment': self.environment,
            'slippage_bps': self.config['transaction']['default_slippage_bps'],
            'max_price_impact': self.config['risk']['max_price_impact_pct'],
            'min_liquidity_usd': self.config['risk']['min_liquidity_usd'],
            'circuit_breaker_enabled': self.config['circuit_breaker']['enabled'],
            'known_tokens': len(self.config['tokens']['known_mints']),
            'api_endpoints': {
                'quote': self.config['api']['quote_url'],
                'swap': self.config['api']['swap_url'],
                'price': self.config['api']['price_url']
            }
        }
        
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive Jupiter configuration test."""
        logger.info("🚀 JUPITER CONFIGURATION COMPREHENSIVE TEST")
        logger.info("=" * 60)
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'environment': self.environment,
            'tests': {},
            'overall_status': 'UNKNOWN'
        }
        
        # Test 1: Configuration Loading
        logger.info("\n📋 Test 1: Configuration Loading")
        config_loaded = await self.load_config()
        results['tests']['config_loading'] = config_loaded
        
        if not config_loaded:
            results['overall_status'] = 'FAILED'
            return results
            
        # Test 2: Configuration Validation
        logger.info("\n🔍 Test 2: Configuration Validation")
        validation = await self.validate_configuration()
        results['tests']['config_validation'] = validation['valid']
        results['validation_details'] = validation
        
        # Test 3: API Connectivity
        logger.info("\n🔗 Test 3: API Connectivity")
        connectivity = await self.test_jupiter_connectivity()
        results['tests']['api_connectivity'] = connectivity
        
        # Test 4: Quote Functionality
        logger.info("\n💱 Test 4: Quote Functionality")
        quote_test = await self.test_jupiter_quote()
        results['tests']['quote_functionality'] = quote_test
        
        # Calculate overall status
        test_results = list(results['tests'].values())
        if all(test_results):
            results['overall_status'] = 'PASSED'
        elif any(test_results):
            results['overall_status'] = 'PARTIAL'
        else:
            results['overall_status'] = 'FAILED'
            
        # Generate summary
        results['config_summary'] = self.generate_config_summary()
        
        # Log final results
        logger.info("\n" + "=" * 60)
        logger.info("📊 JUPITER CONFIGURATION TEST RESULTS")
        logger.info("=" * 60)
        
        for test_name, result in results['tests'].items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
            
        logger.info(f"\nOverall Status: {results['overall_status']}")
        
        if validation['warnings']:
            logger.info(f"\n⚠️ Warnings ({len(validation['warnings'])}):")
            for warning in validation['warnings']:
                logger.info(f"  • {warning}")
                
        if validation['recommendations']:
            logger.info(f"\n💡 Recommendations ({len(validation['recommendations'])}):")
            for rec in validation['recommendations']:
                logger.info(f"  • {rec}")
                
        return results

async def main():
    """Main function to run Jupiter configuration setup."""
    manager = JupiterConfigManager()
    
    try:
        await manager.initialize()
        results = await manager.run_comprehensive_test()
        
        # Save results
        output_file = f"output/jupiter_config_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
            
        logger.info(f"\n📄 Test results saved to: {output_file}")
        
        # Exit with appropriate code
        if results['overall_status'] == 'PASSED':
            logger.info("🎉 Jupiter configuration is ready for production!")
            sys.exit(0)
        elif results['overall_status'] == 'PARTIAL':
            logger.warning("⚠️ Jupiter configuration has some issues but is functional")
            sys.exit(1)
        else:
            logger.error("❌ Jupiter configuration has critical issues")
            sys.exit(2)
            
    except Exception as e:
        logger.error(f"❌ Jupiter configuration test failed: {e}")
        sys.exit(3)
    finally:
        await manager.close()

if __name__ == "__main__":
    asyncio.run(main())
