#!/usr/bin/env python3
"""
Enhanced Live Trading with Critical Issue Fixes
Addresses signature verification failures and RPC endpoint issues.
"""

import asyncio
import logging
import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.transaction.enhanced_tx_builder import EnhancedTransactionBuilder
from core.transaction.enhanced_tx_executor import EnhancedTransactionExecutor
from core.dex.orca_swap_builder import OrcaSwapBuilder
from solders.keypair import Keypair
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedLiveTrader:
    """Enhanced live trader with critical issue fixes."""
    
    def __init__(self):
        """Initialize enhanced live trader."""
        # Environment variables
        self.wallet_address = os.environ.get('WALLET_ADDRESS')
        self.keypair_path = os.environ.get('KEYPAIR_PATH', 'wallet/trading_wallet_keypair.json')
        self.helius_api_key = os.environ.get('HELIUS_API_KEY', 'dda9f776-9a40-447d-9ca4-22a27c21169e')
        
        # RPC configuration with premium endpoints
        self.primary_rpc = f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}"
        self.backup_rpcs = [
            "https://api.mainnet-beta.solana.com",
            f"https://rpc.helius.xyz/?api-key={self.helius_api_key}",
            "https://solana-api.projectserum.com"
        ]
        
        # Components
        self.keypair = None
        self.tx_builder = None
        self.tx_executor = None
        self.swap_builder = None
        
        # Trading metrics
        self.trades_attempted = 0
        self.trades_successful = 0
        self.signature_failures = 0
        self.rpc_failures = 0
        
        logger.info("🚀 Enhanced live trader initialized")
    
    async def initialize(self) -> bool:
        """Initialize all components."""
        try:
            logger.info("🔧 Initializing enhanced trading components...")
            
            # Load keypair
            if not await self._load_keypair():
                return False
            
            # Initialize enhanced transaction builder
            self.tx_builder = EnhancedTransactionBuilder(
                rpc_url=self.primary_rpc,
                keypair=self.keypair,
                backup_rpcs=self.backup_rpcs
            )
            
            # Initialize enhanced transaction executor
            self.tx_executor = EnhancedTransactionExecutor(
                primary_rpc=self.primary_rpc,
                backup_rpcs=self.backup_rpcs,
                jito_enabled=True
            )
            
            # Initialize swap builder
            self.swap_builder = OrcaSwapBuilder(self.wallet_address, self.keypair_path)
            await self.swap_builder.initialize()
            
            logger.info("✅ All enhanced components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize enhanced components: {e}")
            return False
    
    async def _load_keypair(self) -> bool:
        """Load keypair from file."""
        try:
            if not os.path.exists(self.keypair_path):
                logger.error(f"❌ Keypair file not found: {self.keypair_path}")
                return False
            
            with open(self.keypair_path, 'r') as f:
                keypair_data = json.load(f)
            
            self.keypair = Keypair.from_bytes(bytes(keypair_data))
            
            # Verify keypair matches wallet address
            if str(self.keypair.pubkey()) != self.wallet_address:
                logger.warning(f"⚠️ Keypair mismatch: {self.keypair.pubkey()} != {self.wallet_address}")
            
            logger.info("✅ Keypair loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load keypair: {e}")
            return False
    
    async def get_wallet_balance(self) -> float:
        """Get current wallet balance."""
        try:
            import httpx
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                payload = {
                    'jsonrpc': '2.0',
                    'id': 1,
                    'method': 'getBalance',
                    'params': [self.wallet_address]
                }
                
                response = await client.post(self.primary_rpc, json=payload)
                response.raise_for_status()
                result = response.json()
                
                if 'result' in result:
                    balance_lamports = result['result']['value']
                    balance_sol = balance_lamports / 1_000_000_000
                    return balance_sol
                
                return 0.0
                
        except Exception as e:
            logger.error(f"❌ Error getting wallet balance: {e}")
            return 0.0
    
    async def execute_enhanced_trade(self, signal: dict) -> dict:
        """
        Execute trade with enhanced transaction handling.
        
        Args:
            signal: Trading signal
            
        Returns:
            Execution result
        """
        self.trades_attempted += 1
        logger.info(f"🚀 Executing enhanced trade #{self.trades_attempted}: {signal.get('market', 'Unknown')}")
        
        try:
            # Get wallet balance before trade
            balance_before = await self.get_wallet_balance()
            logger.info(f"💰 Balance before trade: {balance_before:.6f} SOL")
            
            # Build enhanced transaction
            async with self.tx_builder:
                transaction = await self.tx_builder.build_jupiter_transaction_enhanced(signal)
                
                if not transaction:
                    logger.error("❌ Failed to build enhanced transaction")
                    return {'success': False, 'error': 'Transaction building failed'}
            
            # Execute with enhanced retry logic
            async with self.tx_executor:
                result = await self.tx_executor.execute_transaction_with_retry(transaction)
                
                if result.get('success'):
                    self.trades_successful += 1
                    logger.info(f"✅ Trade executed successfully: {result.get('signature', 'N/A')}")
                    
                    # Wait for balance update
                    await asyncio.sleep(3)
                    balance_after = await self.get_wallet_balance()
                    balance_change = balance_after - balance_before
                    
                    logger.info(f"💰 Balance after trade: {balance_after:.6f} SOL")
                    logger.info(f"📈 Balance change: {balance_change:+.6f} SOL")
                    
                    # Enhanced result with balance validation
                    result.update({
                        'balance_before': balance_before,
                        'balance_after': balance_after,
                        'balance_change': balance_change,
                        'balance_changed': abs(balance_change) > 0.000001,  # Account for precision
                        'trade_number': self.trades_attempted
                    })
                    
                    return result
                else:
                    error = result.get('error', 'Unknown error')
                    logger.error(f"❌ Trade execution failed: {error}")
                    
                    # Track failure types
                    if 'signature' in error.lower():
                        self.signature_failures += 1
                    elif 'rpc' in error.lower() or 'endpoint' in error.lower():
                        self.rpc_failures += 1
                    
                    return result
                    
        except Exception as e:
            logger.error(f"❌ Error in enhanced trade execution: {e}")
            return {'success': False, 'error': str(e)}
    
    async def run_enhanced_trading_session(self, duration_minutes: int = 5) -> bool:
        """
        Run enhanced trading session with critical fixes.
        
        Args:
            duration_minutes: Duration to run trading
            
        Returns:
            True if session completed successfully
        """
        logger.info(f"🚀 Starting enhanced trading session for {duration_minutes} minutes")
        
        # Initialize components
        if not await self.initialize():
            logger.error("❌ Failed to initialize components")
            return False
        
        # Check initial balance
        initial_balance = await self.get_wallet_balance()
        if initial_balance <= 0:
            logger.error("❌ Invalid wallet balance")
            return False
        
        logger.info(f"💰 Starting balance: {initial_balance:.6f} SOL")
        
        # Run trading session
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        try:
            while datetime.now() < end_time:
                # Generate test signal
                test_signal = {
                    'action': 'BUY',
                    'market': 'SOL-USDC',
                    'size': 0.001,  # Small test size
                    'price': 0,     # Will fetch real-time price
                    'confidence': 0.8,
                    'timestamp': datetime.now().isoformat()
                }
                
                # Execute trade
                result = await self.execute_enhanced_trade(test_signal)
                
                # Log result
                if result.get('success'):
                    logger.info(f"✅ Trade successful: {result.get('execution_type', 'unknown')}")
                    if result.get('balance_changed'):
                        logger.info("🎉 BALANCE CHANGE CONFIRMED - Real trade executed!")
                else:
                    logger.warning(f"⚠️ Trade failed: {result.get('error', 'Unknown error')}")
                
                # Wait before next trade (30 seconds for 5-minute test)
                await asyncio.sleep(30)
            
            # Final report
            await self._generate_session_report(initial_balance)
            return True
            
        except Exception as e:
            logger.error(f"❌ Error in trading session: {e}")
            return False
    
    async def _generate_session_report(self, initial_balance: float):
        """Generate comprehensive session report."""
        final_balance = await self.get_wallet_balance()
        balance_change = final_balance - initial_balance
        success_rate = (self.trades_successful / self.trades_attempted * 100) if self.trades_attempted > 0 else 0
        
        logger.info(f"\n{'='*60}")
        logger.info("📊 ENHANCED TRADING SESSION REPORT")
        logger.info(f"{'='*60}")
        
        logger.info(f"💰 BALANCE ANALYSIS:")
        logger.info(f"   Initial balance:  {initial_balance:.6f} SOL")
        logger.info(f"   Final balance:    {final_balance:.6f} SOL")
        logger.info(f"   Balance change:   {balance_change:+.6f} SOL")
        
        logger.info(f"\n📈 TRADING STATISTICS:")
        logger.info(f"   Trades attempted:     {self.trades_attempted}")
        logger.info(f"   Trades successful:    {self.trades_successful}")
        logger.info(f"   Success rate:         {success_rate:.1f}%")
        
        logger.info(f"\n🔧 ISSUE RESOLUTION:")
        logger.info(f"   Signature failures:   {self.signature_failures}")
        logger.info(f"   RPC failures:         {self.rpc_failures}")
        
        if balance_change != 0:
            logger.info(f"\n🎉 SUCCESS: Balance changed - Real trades executed!")
            logger.info(f"✅ Critical issues resolved - Enhanced system working!")
        else:
            logger.info(f"\n⚠️ No balance change detected")
        
        logger.info(f"{'='*60}")

async def main():
    """Main function for enhanced live trading."""
    logger.info("🚀 Enhanced Live Trading with Critical Issue Fixes")
    logger.info("⚠️ This will execute REAL TRADES with enhanced reliability")
    
    # Verify environment
    wallet_address = os.environ.get('WALLET_ADDRESS')
    if not wallet_address:
        logger.error("❌ WALLET_ADDRESS not found in environment")
        return False
    
    logger.info(f"🔑 Using wallet: {wallet_address[:8]}...{wallet_address[-8:]}")
    
    # Create enhanced trader
    trader = EnhancedLiveTrader()
    
    # Run enhanced trading session
    success = await trader.run_enhanced_trading_session(duration_minutes=5)
    
    if success:
        logger.info("✅ Enhanced trading session completed successfully")
    else:
        logger.error("❌ Enhanced trading session failed")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
