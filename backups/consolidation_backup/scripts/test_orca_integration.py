#!/usr/bin/env python3
"""
Test Orca Integration
This script tests the new Orca integration to ensure it works correctly.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_orca_integration():
    """Test the Orca integration components."""
    logger.info("🧪 TESTING ORCA INTEGRATION")
    logger.info("="*60)
    
    try:
        # Test 1: Import Orca components
        logger.info("📦 Testing Orca component imports...")
        try:
            from core.dex.orca_client import OrcaClient, get_token_mint, COMMON_TOKENS
            from core.dex.orca_swap_builder import OrcaSwapBuilder
            logger.info("✅ Orca components imported successfully")
        except ImportError as e:
            logger.error(f"❌ Failed to import Orca components: {e}")
            return False
        
        # Test 2: Initialize Orca client
        logger.info("🔧 Testing Orca client initialization...")
        try:
            orca_client = OrcaClient()
            logger.info("✅ Orca client initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Orca client: {e}")
            return False
        
        # Test 3: Test token mint resolution
        logger.info("🪙 Testing token mint resolution...")
        try:
            sol_mint = get_token_mint('SOL')
            usdc_mint = get_token_mint('USDC')
            logger.info(f"✅ SOL mint: {sol_mint}")
            logger.info(f"✅ USDC mint: {usdc_mint}")
            
            if sol_mint != COMMON_TOKENS['SOL']:
                logger.error("❌ SOL mint mismatch")
                return False
            if usdc_mint != COMMON_TOKENS['USDC']:
                logger.error("❌ USDC mint mismatch")
                return False
                
            logger.info("✅ Token mint resolution working correctly")
        except Exception as e:
            logger.error(f"❌ Token mint resolution failed: {e}")
            return False
        
        # Test 4: Test pool information retrieval
        logger.info("🏊 Testing pool information retrieval...")
        try:
            pool_info = await orca_client.get_pool_info(sol_mint, usdc_mint)
            if pool_info:
                logger.info(f"✅ Pool info retrieved: {pool_info.get('fee_tier', 'Unknown')} fee tier")
            else:
                logger.warning("⚠️ No pool info found (expected for static config)")
            logger.info("✅ Pool information retrieval working")
        except Exception as e:
            logger.error(f"❌ Pool information retrieval failed: {e}")
            return False
        
        # Test 5: Test quote generation
        logger.info("💰 Testing quote generation...")
        try:
            quote = await orca_client.get_quote(sol_mint, usdc_mint, 1000000)  # 0.001 SOL
            if quote:
                logger.info(f"✅ Quote generated: {quote.get('estimated_output', 0)} output")
                logger.info(f"✅ Min output: {quote.get('min_output', 0)}")
                logger.info(f"✅ Slippage: {quote.get('slippage_bps', 0)} bps")
            else:
                logger.error("❌ Failed to generate quote")
                return False
        except Exception as e:
            logger.error(f"❌ Quote generation failed: {e}")
            return False
        
        # Test 6: Initialize swap builder
        logger.info("🔨 Testing swap builder initialization...")
        try:
            wallet_address = os.environ.get('WALLET_ADDRESS')
            if not wallet_address:
                logger.error("❌ WALLET_ADDRESS not found in environment")
                return False
            
            swap_builder = OrcaSwapBuilder(wallet_address)
            await swap_builder.initialize()
            logger.info("✅ Swap builder initialized successfully")
        except Exception as e:
            logger.error(f"❌ Swap builder initialization failed: {e}")
            return False
        
        # Test 7: Test signal validation
        logger.info("✅ Testing signal validation...")
        try:
            # Valid signal
            valid_signal = {
                'action': 'BUY',
                'market': 'SOL-USDC',
                'price': 180.0,
                'size': 0.001,
                'confidence': 0.8,
                'timestamp': datetime.now().isoformat()
            }
            
            is_valid = await swap_builder.validate_swap_parameters(valid_signal)
            if is_valid:
                logger.info("✅ Valid signal validation passed")
            else:
                logger.error("❌ Valid signal validation failed")
                return False
            
            # Invalid signal
            invalid_signal = {
                'action': 'INVALID',
                'market': 'INVALID',
                'price': -1,
                'size': 0
            }
            
            is_invalid = await swap_builder.validate_swap_parameters(invalid_signal)
            if not is_invalid:
                logger.info("✅ Invalid signal validation correctly rejected")
            else:
                logger.error("❌ Invalid signal validation incorrectly passed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Signal validation failed: {e}")
            return False
        
        # Test 8: Test transaction building (dry run)
        logger.info("🔧 Testing transaction building (dry run)...")
        try:
            test_signal = {
                'action': 'BUY',
                'market': 'SOL-USDC',
                'price': 180.0,
                'size': 0.001,
                'confidence': 0.8,
                'timestamp': datetime.now().isoformat()
            }
            
            # This will build a simplified transaction
            tx_result = await swap_builder.build_swap_transaction(test_signal)
            if tx_result and tx_result.get('success'):
                logger.info("✅ Transaction building successful")
                logger.info(f"✅ Execution type: {tx_result.get('execution_type')}")
                logger.info(f"✅ Input amount: {tx_result.get('input_amount', 0)}")
                logger.info(f"✅ Estimated output: {tx_result.get('estimated_output', 0)}")
            else:
                error = tx_result.get('error', 'Unknown error') if tx_result else 'No result'
                logger.error(f"❌ Transaction building failed: {error}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Transaction building failed: {e}")
            return False
        
        # Test 9: Test supported tokens
        logger.info("📋 Testing supported tokens...")
        try:
            supported_tokens = await swap_builder.get_supported_tokens()
            logger.info(f"✅ Supported tokens: {list(supported_tokens.keys())}")
            
            if 'SOL' not in supported_tokens or 'USDC' not in supported_tokens:
                logger.error("❌ Missing required tokens in supported list")
                return False
                
        except Exception as e:
            logger.error(f"❌ Supported tokens test failed: {e}")
            return False
        
        # Cleanup
        await orca_client.close()
        await swap_builder.close()
        
        logger.info("\n" + "="*60)
        logger.info("🎉 ALL ORCA INTEGRATION TESTS PASSED!")
        logger.info("✅ Orca integration is ready for live trading")
        logger.info("="*60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Unexpected error in Orca integration test: {e}")
        return False

async def main():
    """Main function."""
    print("\n" + "="*60)
    print("ORCA INTEGRATION TEST")
    print("="*60)
    print("This test validates the Orca integration components")
    print("without executing any real transactions.")
    print("="*60)
    
    # Run the test
    success = await test_orca_integration()
    
    if success:
        print("\n✅ ORCA INTEGRATION TEST SUCCESSFUL")
        print("🌊 Orca integration is ready to replace Jupiter")
        print("🚀 Ready to test live trading with Orca")
        return 0
    else:
        print("\n❌ ORCA INTEGRATION TEST FAILED")
        print("🔧 Please fix the issues before proceeding")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
