#!/usr/bin/env python3
"""
Jupiter Files Cleanup Script

This script identifies and removes irrelevant Jupiter files to avoid confusion
while keeping the current production Jupiter integration.
"""

import os
import sys
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Set
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('jupiter_cleanup')

class JupiterFilesCleanup:
    """Manager for Jupiter files cleanup."""

    def __init__(self):
        self.root_dir = Path.cwd()
        self.backup_dir = None

        # Files to KEEP (current production Jupiter system)
        self.keep_files = {
            'config/jupiter_config.yaml',                    # Main Jupiter configuration
            'scripts/setup_jupiter_config.py',              # Jupiter configuration manager
            'scripts/test_jupiter_swap.py',                  # Current Jupiter test
            'phase_4_deployment/rpc_execution/tx_builder.py', # Main transaction builder with Jupiter
            'solana_tx_utils/src/dex/jupiter.rs',           # Rust Jupiter implementation
        }

        # Files to REMOVE (old/duplicate Jupiter files)
        self.remove_files = {
            # Documentation files that are no longer needed (cleanup completed)
            'JUPITER_CLEANUP_PLAN.md',  # Cleanup plan - no longer needed after completion

            # Old Jupiter API keys in backup configs
            'backups/cleanup_backup_20250524_153642/phase_0_env_setup/config/system_config.yaml',
            'backups/cleanup_backup_20250524_153642/backups/cleanup_backup_20250524_153642/phase_0_env_setup/config/system_config.yaml',
            'backups/cleanup_backup_20250524_153642/backups/cleanup_backup_20250524_153642/backups/cleanup_backup_20250524_153642/phase_0_env_setup/config/system_config.yaml',
            'backups/cleanup_backup_20250524_153642/backups/cleanup_backup_20250524_153642/backups/cleanup_backup_20250524_153642/backups/cleanup_backup_20250524_153642/phase_0_env_setup/config/system_config.yaml',

            # Old Jupiter test files (already in depr.txt but may still exist)
            'test_jupiter.py',
            'test_jupiter_api.py',
            'jupiter_test.py',
            'test_jupiter_integration.py',

            # Old Jupiter configuration files
            'config/jupiter.yaml',
            'config/jupiter_api.yaml',
            'configs/jupiter_config.yaml',
            'phase_4_deployment/configs/jupiter_config.yaml',

            # Old Jupiter implementations
            'jupiter_client.py',
            'jupiter_api.py',
            'jupiter_integration.py',
            'jupiter_swap.py',

            # Duplicate Jupiter utilities
            'utils/jupiter_utils.py',
            'shared/jupiter_utils.py',
            'phase_4_deployment/utils/jupiter_utils.py',
        }

        # Files to KEEP (additional files that should be preserved)
        self.additional_keep_files = {
            'output/jupiter_config_test_20250524_172525.json',  # Test results - useful for reference
            'scripts/cleanup_jupiter_files.py',                # This cleanup script itself
            'config/whale_wallets.json',                       # Not Jupiter-specific, just contains Jupiter references
            'tests/README.md',                                 # Test documentation
            'tests/test_production_live_trading.py',           # Production tests with Jupiter integration
            'tests/test_transaction_execution_system.py',      # Transaction system tests
        }

        # Directories to check for Jupiter files
        self.search_directories = [
            'backups',
            'phase_0_env_setup',
            'phase_1_strategy_runner',
            'phase_2_backtest_engine',
            'phase_2_strategy',
            'phase_3_rl_agent_training',
            'configs',
            'config',
            'utils',
            'shared',
            'test',
            'tests'
        ]

    def create_backup(self) -> bool:
        """Create backup before cleanup."""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.backup_dir = self.root_dir / f"backups/jupiter_cleanup_backup_{timestamp}"
            self.backup_dir.mkdir(parents=True, exist_ok=True)

            logger.info(f"✅ Created backup directory: {self.backup_dir}")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to create backup: {e}")
            return False

    def find_jupiter_files(self) -> Dict[str, List[Path]]:
        """Find all Jupiter-related files in the system."""
        jupiter_files = {
            'keep': [],
            'remove': [],
            'unknown': []
        }

        # Search for files containing 'jupiter' in name or content
        for root, dirs, files in os.walk(self.root_dir):
            # Skip certain directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', 'target']]

            for file in files:
                file_path = Path(root) / file
                relative_path = file_path.relative_to(self.root_dir)

                # Check if file contains 'jupiter' in name
                if 'jupiter' in file.lower():
                    if str(relative_path) in self.keep_files or str(relative_path) in self.additional_keep_files:
                        jupiter_files['keep'].append(relative_path)
                    elif str(relative_path) in self.remove_files:
                        jupiter_files['remove'].append(relative_path)
                    else:
                        jupiter_files['unknown'].append(relative_path)

                # Check specific files we know should be removed
                elif str(relative_path) in self.remove_files:
                    jupiter_files['remove'].append(relative_path)

        return jupiter_files

    def analyze_file_content(self, file_path: Path) -> bool:
        """Check if file contains Jupiter-related content."""
        try:
            if file_path.suffix in ['.py', '.yaml', '.yml', '.json', '.md', '.txt']:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read().lower()
                    jupiter_keywords = [
                        'jupiter', 'jup.ag', 'quote-api.jup.ag',
                        'jupiter_program_id', 'jupiter_api', 'jupiter_swap'
                    ]
                    return any(keyword in content for keyword in jupiter_keywords)
        except Exception:
            pass
        return False

    def backup_file(self, file_path: Path) -> bool:
        """Backup a file before removal."""
        try:
            if not self.backup_dir:
                return False

            backup_path = self.backup_dir / file_path
            backup_path.parent.mkdir(parents=True, exist_ok=True)

            if file_path.exists():
                shutil.copy2(file_path, backup_path)
                logger.debug(f"Backed up: {file_path}")
                return True

        except Exception as e:
            logger.warning(f"Failed to backup {file_path}: {e}")

        return False

    def remove_file(self, file_path: Path) -> bool:
        """Remove a file safely."""
        try:
            if file_path.exists():
                # Backup first
                self.backup_file(file_path)

                # Remove the file
                file_path.unlink()
                logger.info(f"🗑️ Removed: {file_path}")
                return True
            else:
                logger.debug(f"File not found (already removed): {file_path}")
                return True

        except Exception as e:
            logger.error(f"❌ Failed to remove {file_path}: {e}")
            return False

    def cleanup_jupiter_files(self, dry_run: bool = True) -> Dict[str, int]:
        """Clean up Jupiter files."""
        logger.info("🔍 JUPITER FILES CLEANUP")
        logger.info("=" * 50)

        # Find all Jupiter files
        jupiter_files = self.find_jupiter_files()

        # Additional search for files with Jupiter content
        logger.info("🔍 Searching for additional Jupiter files...")
        for search_dir in self.search_directories:
            search_path = self.root_dir / search_dir
            if search_path.exists():
                for file_path in search_path.rglob('*'):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(self.root_dir)

                        # Skip if already categorized
                        if (relative_path in jupiter_files['keep'] or
                            relative_path in jupiter_files['remove'] or
                            relative_path in jupiter_files['unknown']):
                            continue

                        # Check content for Jupiter references
                        if self.analyze_file_content(file_path):
                            if str(relative_path) in self.keep_files or str(relative_path) in self.additional_keep_files:
                                jupiter_files['keep'].append(relative_path)
                            elif str(relative_path) in self.remove_files:
                                jupiter_files['remove'].append(relative_path)
                            else:
                                jupiter_files['unknown'].append(relative_path)

        # Report findings
        logger.info(f"\n📊 JUPITER FILES ANALYSIS:")
        logger.info(f"Files to KEEP: {len(jupiter_files['keep'])}")
        logger.info(f"Files to REMOVE: {len(jupiter_files['remove'])}")
        logger.info(f"Unknown Jupiter files: {len(jupiter_files['unknown'])}")

        # Show files to keep
        if jupiter_files['keep']:
            logger.info(f"\n✅ FILES TO KEEP (Production Jupiter System):")
            for file_path in sorted(jupiter_files['keep']):
                logger.info(f"  ✅ {file_path}")

        # Show files to remove
        if jupiter_files['remove']:
            logger.info(f"\n🗑️ FILES TO REMOVE (Old/Duplicate Jupiter Files):")
            for file_path in sorted(jupiter_files['remove']):
                logger.info(f"  🗑️ {file_path}")

        # Show unknown files for review
        if jupiter_files['unknown']:
            logger.info(f"\n❓ UNKNOWN JUPITER FILES (Need Review):")
            for file_path in sorted(jupiter_files['unknown']):
                logger.info(f"  ❓ {file_path}")

        results = {
            'keep': len(jupiter_files['keep']),
            'remove': len(jupiter_files['remove']),
            'unknown': len(jupiter_files['unknown']),
            'removed': 0,
            'failed': 0
        }

        # Perform cleanup if not dry run
        if not dry_run:
            logger.info(f"\n🧹 PERFORMING CLEANUP...")

            # Create backup
            if not self.create_backup():
                logger.error("❌ Failed to create backup, aborting cleanup")
                return results

            # Remove files
            for file_path in jupiter_files['remove']:
                full_path = self.root_dir / file_path
                if self.remove_file(full_path):
                    results['removed'] += 1
                else:
                    results['failed'] += 1

            logger.info(f"\n📊 CLEANUP RESULTS:")
            logger.info(f"Files removed: {results['removed']}")
            logger.info(f"Removal failures: {results['failed']}")

            if self.backup_dir:
                logger.info(f"Backup created: {self.backup_dir}")
        else:
            logger.info(f"\n🔍 DRY RUN - No files were actually removed")
            logger.info(f"Run with --execute to perform actual cleanup")

        return results

def main():
    """Main function."""
    import argparse

    parser = argparse.ArgumentParser(description='Clean up irrelevant Jupiter files')
    parser.add_argument('--execute', action='store_true',
                       help='Actually perform the cleanup (default is dry run)')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    cleanup = JupiterFilesCleanup()

    try:
        results = cleanup.cleanup_jupiter_files(dry_run=not args.execute)

        logger.info("\n" + "=" * 50)
        logger.info("🎯 JUPITER CLEANUP SUMMARY")
        logger.info("=" * 50)
        logger.info(f"Production files preserved: {results['keep']}")
        logger.info(f"Files identified for removal: {results['remove']}")
        logger.info(f"Unknown files requiring review: {results['unknown']}")

        if args.execute:
            logger.info(f"Files successfully removed: {results['removed']}")
            logger.info(f"Removal failures: {results['failed']}")

            if results['failed'] == 0:
                logger.info("✅ Jupiter cleanup completed successfully!")
                sys.exit(0)
            else:
                logger.warning("⚠️ Jupiter cleanup completed with some failures")
                sys.exit(1)
        else:
            logger.info("🔍 Dry run completed - use --execute to perform cleanup")
            sys.exit(0)

    except Exception as e:
        logger.error(f"❌ Jupiter cleanup failed: {e}")
        sys.exit(2)

if __name__ == "__main__":
    main()
