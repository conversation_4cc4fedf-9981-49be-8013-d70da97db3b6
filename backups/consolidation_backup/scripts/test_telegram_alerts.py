#!/usr/bin/env python3
"""
Test Telegram Alert System
Verifies that Telegram notifications are working correctly.
"""

import asyncio
import os
import sys
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.notifications.telegram_notifier import TelegramNotifier

async def test_telegram_alerts():
    """Test all Telegram alert functions."""

    print("🧪 Testing Telegram Alert System")
    print("=" * 40)

    # Initialize notifier
    notifier = TelegramNotifier()

    if not notifier.enabled:
        print("❌ Telegram not configured!")
        print("Please check your .env file for:")
        print("  - TELEGRAM_BOT_TOKEN")
        print("  - TELEGRAM_CHAT_ID")
        return False

    print(f"✅ Telegram configured for chat: {notifier.chat_id}")
    print()

    try:
        # Test 1: Basic connection test
        print("🔄 Testing basic connection...")
        test_result = await notifier.test_connection()
        if test_result:
            print("✅ Connection test successful!")
        else:
            print("❌ Connection test failed!")
            return False

        await asyncio.sleep(2)  # Rate limiting

        # Test 2: Trade execution notification
        print("🔄 Testing trade execution notification...")
        sample_trade_data = {
            'signal': {
                'action': 'BUY',
                'size': 0.1234,
                'confidence': 0.85,
                'price': 180.50,
                'strategy': 'Enhanced Momentum'
            },
            'position_data': {
                'position_size_sol': 0.1234,
                'position_size_usd': 22.27
            },
            'transaction_result': {
                'success': True,
                'signature': '5zdGrUpKJi6EbPL2mNxqVQKjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHjHj',
                'execution_time': 0.125
            },
            'timestamp': datetime.now().isoformat()
        }

        trade_result = await notifier.notify_trade_executed(sample_trade_data)
        if trade_result:
            print("✅ Trade execution notification sent!")
        else:
            print("❌ Trade execution notification failed!")

        await asyncio.sleep(2)  # Rate limiting

        # Test 3: Trade rejection notification
        print("🔄 Testing trade rejection notification...")
        sample_signal = {
            'action': 'SELL',
            'size': 0.0987,
            'confidence': 0.65
        }

        rejection_result = await notifier.notify_trade_rejected(sample_signal, "Insufficient balance")
        if rejection_result:
            print("✅ Trade rejection notification sent!")
        else:
            print("❌ Trade rejection notification failed!")

        await asyncio.sleep(2)  # Rate limiting

        # Test 4: Session start notification with PnL tracking
        print("🔄 Testing session start notification with PnL tracking...")
        session_start_result = await notifier.notify_session_started(0.5, 3.947251432)  # Sample balance
        if session_start_result:
            print("✅ Session start notification with PnL tracking sent!")
        else:
            print("❌ Session start notification failed!")

        await asyncio.sleep(2)  # Rate limiting

        # Test 5: Session end notification with PnL summary
        print("🔄 Testing session end notification with PnL summary...")
        sample_metrics = {
            'cycles_completed': 15,
            'trades_executed': 8,
            'trades_rejected': 2,
            'session_duration_minutes': 30.5
        }

        # Test with profit scenario
        final_balance = 3.952251432  # +0.005 SOL profit
        avg_price = 180.24

        session_end_result = await notifier.notify_session_ended(sample_metrics, final_balance, avg_price)
        if session_end_result:
            print("✅ Session end notification with PnL summary sent!")
        else:
            print("❌ Session end notification failed!")

        await asyncio.sleep(2)  # Rate limiting

        # Test 6: Error notification
        print("🔄 Testing error notification...")
        error_result = await notifier.notify_error("Test error message", "Test Component")
        if error_result:
            print("✅ Error notification sent!")
        else:
            print("❌ Error notification failed!")

        # Test 7: PnL milestone notification
        print("🔄 Testing PnL milestone notification...")
        sample_pnl_metrics = {
            'pnl_sol': 0.005,
            'pnl_usd': 0.90,
            'pnl_percent': 0.13,
            'current_balance': 3.952251432,
            'start_balance': 3.947251432
        }

        milestone_result = await notifier.notify_pnl_milestone(sample_pnl_metrics, "profit")
        if milestone_result:
            print("✅ PnL milestone notification sent!")
        else:
            print("❌ PnL milestone notification failed!")

        await asyncio.sleep(2)  # Rate limiting

        print()
        print("🎉 All Telegram tests completed!")
        print("Check your Telegram chat to verify all messages were received.")
        print("📊 New PnL features tested:")
        print("   - Session start with balance tracking")
        print("   - Trade execution with real-time PnL")
        print("   - Session end with PnL summary")
        print("   - PnL milestone notifications")

        return True

    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

    finally:
        # Close the notifier
        await notifier.close()

async def test_live_trading_integration():
    """Test integration with live trading system."""

    print("\n🔗 Testing Live Trading Integration")
    print("=" * 40)

    # Check if live trading scripts have Telegram integration
    live_trading_scripts = [
        "scripts/unified_live_trading.py",
        "scripts/start_live_production.py",
        "scripts/rl_enhanced_live_trading.py",
        "phase_4_deployment/start_live_trading.py"
    ]

    # Find the first available script
    script_path = None
    for script in live_trading_scripts:
        if os.path.exists(script):
            script_path = script
            break

    if not script_path:
        print("❌ No live trading scripts found!")
        print(f"Searched for: {', '.join(live_trading_scripts)}")
        return False

    print(f"✅ Testing integration in: {script_path}")

    with open(script_path, 'r') as f:
        content = f.read()

    # Check for Telegram imports and usage (flexible checks)
    checks = [
        ("TelegramNotifier import", ["from core.notifications.telegram_notifier import TelegramNotifier", "TelegramNotifier"]),
        ("Telegram initialization", ["telegram_notifier = TelegramNotifier()", "self.telegram_notifier"]),
        ("Trade execution notification", ["notify_trade_executed", "trade_executed"]),
        ("Session start notification", ["notify_session_started", "session_started"]),
        ("Session end notification", ["notify_session_ended", "session_ended"])
    ]

    all_checks_passed = True

    for check_name, check_strings in checks:
        # Check if any of the possible strings are found
        found = any(check_string in content for check_string in check_strings)
        if found:
            print(f"✅ {check_name}: Found")
        else:
            print(f"❌ {check_name}: Missing")
            all_checks_passed = False

    if all_checks_passed:
        print("✅ Live trading integration is complete!")
    else:
        print("⚠️ Some integration components are missing")

    return all_checks_passed

def main():
    """Main test function."""
    print("🚀 Synergy7 Telegram Alert System Test")
    print("=" * 50)
    print()

    # Check environment variables
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    chat_id = os.getenv('TELEGRAM_CHAT_ID')

    if not bot_token or not chat_id:
        print("❌ Telegram credentials not found in environment!")
        print("Please ensure your .env file contains:")
        print("  TELEGRAM_BOT_TOKEN=your_bot_token")
        print("  TELEGRAM_CHAT_ID=your_chat_id")
        return 1

    print(f"📱 Bot Token: {bot_token[:20]}...")
    print(f"💬 Chat ID: {chat_id}")
    print()

    # Run async tests
    try:
        # Test Telegram alerts
        alert_success = asyncio.run(test_telegram_alerts())

        # Test integration
        integration_success = asyncio.run(test_live_trading_integration())

        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)

        if alert_success:
            print("✅ Telegram Alert System: WORKING")
        else:
            print("❌ Telegram Alert System: FAILED")

        if integration_success:
            print("✅ Live Trading Integration: COMPLETE")
        else:
            print("❌ Live Trading Integration: INCOMPLETE")

        if alert_success and integration_success:
            print("\n🎉 ALL TESTS PASSED!")
            print("Your Telegram alert system is ready for live trading!")
            return 0
        else:
            print("\n⚠️ SOME TESTS FAILED!")
            print("Please check the issues above before using live trading.")
            return 1

    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
