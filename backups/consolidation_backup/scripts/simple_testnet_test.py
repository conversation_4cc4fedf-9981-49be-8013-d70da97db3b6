#!/usr/bin/env python3
"""
Simple Testnet Test
Bypasses complex keypair loading and tests basic system functionality on testnet.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_testnet_basic_functionality():
    """Test basic testnet functionality without complex keypair loading."""
    logger.info("🧪 SIMPLE TESTNET FUNCTIONALITY TEST")
    logger.info("=" * 50)
    
    # Load testnet environment
    load_dotenv('.env.testnet')
    
    # Test 1: Environment validation
    logger.info("🔧 Testing environment validation...")
    wallet_address = os.getenv('WALLET_ADDRESS')
    helius_api_key = os.getenv('HELIUS_API_KEY')
    
    if not wallet_address:
        logger.error("❌ WALLET_ADDRESS not set")
        return False
    
    if not helius_api_key:
        logger.error("❌ HELIUS_API_KEY not set")
        return False
    
    logger.info(f"✅ Wallet address: {wallet_address}")
    logger.info("✅ Environment validation passed")
    
    # Test 2: RPC connectivity
    logger.info("🌐 Testing testnet RPC connectivity...")
    try:
        import httpx
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.testnet.solana.com",
                json={
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getHealth"
                },
                headers={"Content-Type": "application/json"},
                timeout=10.0
            )
            
            if response.status_code == 200:
                logger.info("✅ Testnet RPC connectivity successful")
            else:
                logger.warning(f"⚠️ RPC status: {response.status_code}")
    
    except Exception as e:
        logger.warning(f"⚠️ RPC test failed: {e}")
    
    # Test 3: Wallet balance check
    logger.info("💰 Testing wallet balance check...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.testnet.solana.com",
                json={
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getBalance",
                    "params": [wallet_address]
                },
                headers={"Content-Type": "application/json"},
                timeout=10.0
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'result' in data:
                    balance_lamports = data['result']['value']
                    balance_sol = balance_lamports / 1_000_000_000
                    logger.info(f"💰 Testnet wallet balance: {balance_sol:.6f} SOL")
                    
                    if balance_sol == 0:
                        logger.warning("⚠️ Zero balance - need to request SOL from faucet")
                        logger.info("💡 Visit: https://faucet.solana.com/")
                    else:
                        logger.info("✅ Wallet has SOL balance")
                else:
                    logger.warning("⚠️ Could not get balance")
            else:
                logger.warning(f"⚠️ Balance check failed: {response.status_code}")
    
    except Exception as e:
        logger.warning(f"⚠️ Balance check failed: {e}")
    
    # Test 4: Core module imports
    logger.info("📦 Testing core module imports...")
    try:
        from phase_4_deployment.rpc_execution.helius_client import HeliusClient
        logger.info("✅ HeliusClient import successful")
        
        from phase_4_deployment.rpc_execution.transaction_executor import TransactionExecutor
        logger.info("✅ TransactionExecutor import successful")
        
        from core.notifications.telegram_notifier import TelegramNotifier
        logger.info("✅ TelegramNotifier import successful")
        
    except ImportError as e:
        logger.error(f"❌ Module import failed: {e}")
        return False
    
    # Test 5: Telegram notification
    logger.info("📱 Testing Telegram notification...")
    try:
        telegram_notifier = TelegramNotifier()
        if telegram_notifier.enabled:
            # Send test message
            import httpx
            
            bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
            chat_id = os.getenv('TELEGRAM_CHAT_ID')
            
            if bot_token and chat_id:
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        f"https://api.telegram.org/bot{bot_token}/sendMessage",
                        json={
                            "chat_id": chat_id,
                            "text": "🧪 Testnet functionality test successful!\n\n📍 Wallet: " + wallet_address[:8] + "...\n🌐 Network: Testnet\n⏰ Test completed"
                        },
                        timeout=10.0
                    )
                    
                    if response.status_code == 200:
                        logger.info("✅ Telegram test message sent")
                    else:
                        logger.warning(f"⚠️ Telegram failed: {response.status_code}")
            else:
                logger.warning("⚠️ Telegram credentials not found")
        else:
            logger.warning("⚠️ Telegram notifier disabled")
    
    except Exception as e:
        logger.warning(f"⚠️ Telegram test failed: {e}")
    
    # Test 6: Transaction builder (without keypair)
    logger.info("🔨 Testing transaction builder...")
    try:
        from phase_4_deployment.rpc_execution.tx_builder import TxBuilder
        
        # Create builder without keypair for basic testing
        tx_builder = TxBuilder(wallet_address)
        logger.info("✅ Transaction builder created successfully")
        
        # Test signal creation
        test_signal = {
            "action": "BUY",
            "market": "SOL-USDC",
            "price": 180.0,
            "size": 0.001,
            "confidence": 0.8,
            "timestamp": "2025-05-27T00:00:00Z"
        }
        
        logger.info("✅ Test signal created")
        logger.info(f"📊 Signal: {test_signal['action']} {test_signal['market']}")
        
    except Exception as e:
        logger.error(f"❌ Transaction builder test failed: {e}")
        return False
    
    # Test 7: System configuration
    logger.info("⚙️ Testing system configuration...")
    try:
        # Check if testnet config exists
        testnet_config_path = Path("config/environments/testnet.yaml")
        if testnet_config_path.exists():
            logger.info("✅ Testnet configuration file found")
        else:
            logger.warning("⚠️ Testnet configuration file not found")
        
        # Check environment variables
        required_vars = [
            'SOLANA_NETWORK', 'HELIUS_RPC_URL', 'TRADING_ENABLED', 
            'DRY_RUN', 'TESTNET_MODE'
        ]
        
        for var in required_vars:
            value = os.getenv(var)
            if value:
                logger.info(f"✅ {var}: {value}")
            else:
                logger.warning(f"⚠️ {var}: not set")
    
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False
    
    logger.info("\n" + "=" * 50)
    logger.info("🎯 TESTNET BASIC FUNCTIONALITY TEST COMPLETED")
    logger.info("=" * 50)
    logger.info("✅ Core system components are functional on testnet")
    logger.info("✅ RPC connectivity working")
    logger.info("✅ Module imports successful")
    logger.info("✅ Configuration properly loaded")
    logger.info("\n🚀 NEXT STEPS:")
    logger.info("1. Request SOL from faucet: https://faucet.solana.com/")
    logger.info("2. Run unified runner: python3 phase_4_deployment/unified_runner.py --mode live --env-file .env.testnet")
    logger.info("3. Monitor transactions: https://explorer.solana.com/?cluster=testnet")
    
    return True

async def main():
    """Main function."""
    success = await test_testnet_basic_functionality()
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
