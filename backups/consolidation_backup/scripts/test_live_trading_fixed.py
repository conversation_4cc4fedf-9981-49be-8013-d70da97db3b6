#!/usr/bin/env python3
"""
Test Live Trading with Fixed Transaction Execution
This script tests the fixed live trading system with wallet balance validation.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_live_trading_fixed():
    """Test the fixed live trading system."""
    logger.info("🚀 TESTING FIXED LIVE TRADING SYSTEM")
    logger.info("="*60)
    logger.info("⚠️  This will execute REAL TRADES with REAL MONEY")
    logger.info("="*60)
    
    try:
        # Import the unified trading system
        from scripts.unified_live_trading import UnifiedLiveTrader
        
        # Create trader with test configuration
        trader = UnifiedLiveTrader()
        
        # Set test parameters for safety
        trader.max_trades_per_hour = 1  # Limit to 1 trade per hour
        trader.min_confidence_threshold = 0.9  # High confidence only
        trader.immediate_execution = True
        
        # Check wallet balance before starting
        logger.info("💰 Checking wallet balance before trading...")
        initial_balance = await trader.get_wallet_balance()
        logger.info(f"💰 Initial wallet balance: {initial_balance} SOL")
        
        if initial_balance < 0.01:
            logger.error("❌ Insufficient wallet balance for testing")
            return False
        
        # Run a short trading session (5 minutes)
        logger.info("🔄 Starting 5-minute test trading session...")
        success = await trader.run_live_trading(duration_minutes=5)
        
        # Check wallet balance after trading
        logger.info("💰 Checking wallet balance after trading...")
        final_balance = await trader.get_wallet_balance()
        logger.info(f"💰 Final wallet balance: {final_balance} SOL")
        
        # Calculate balance change
        balance_change = final_balance - initial_balance
        logger.info(f"📈 Balance change: {balance_change:.6f} SOL")
        
        if balance_change != 0:
            logger.info("✅ SUCCESS: Wallet balance changed - trades executed successfully!")
        else:
            logger.info("ℹ️  No balance change - no trades executed (normal for test)")
        
        if success:
            logger.info("✅ Fixed live trading test completed successfully")
            return True
        else:
            logger.error("❌ Fixed live trading test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in fixed live trading test: {e}")
        return False

async def main():
    """Main function."""
    print("\n" + "="*60)
    print("SYNERGY7 FIXED LIVE TRADING TEST")
    print("="*60)
    print("This test will:")
    print("1. Initialize the fixed trading system")
    print("2. Check wallet balance before trading")
    print("3. Run a 5-minute live trading session")
    print("4. Validate wallet balance changes")
    print("5. Confirm successful trade execution")
    print("="*60)
    
    # Confirm with user
    response = input("\nProceed with live trading test? (yes/no): ").lower().strip()
    if response not in ['yes', 'y']:
        print("❌ Test cancelled by user")
        return 1
    
    # Run the test
    success = await test_live_trading_fixed()
    
    if success:
        print("\n✅ FIXED LIVE TRADING TEST SUCCESSFUL")
        print("🎯 System is ready for production trading")
        return 0
    else:
        print("\n❌ FIXED LIVE TRADING TEST FAILED")
        print("🔧 System needs additional fixes")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
