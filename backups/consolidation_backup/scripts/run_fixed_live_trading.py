#!/usr/bin/env python3
"""
Live Trading with Fixed Jupiter System
Executes live trading using the blockhash timing fixes and profitability optimizations
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# Add project root to path
sys.path.append('.')

async def run_fixed_live_trading():
    """Run live trading with all fixes applied"""

    print("🚀 STARTING FIXED LIVE TRADING SYSTEM")
    print("=" * 60)
    print("✅ Jupiter blockhash timing: FIXED")
    print("✅ Profitability strategies: APPLIED")
    print("✅ Risk management: ENABLED")
    print("=" * 60)

    # Import the unified trading system
    try:
        from scripts.unified_live_trading import UnifiedLiveTrader

        # Create trader with optimized configuration
        trader = UnifiedLiveTrader()

        # Set optimized parameters
        trader.max_trades_per_hour = 3
        trader.min_confidence_threshold = 0.8
        trader.immediate_execution = True

        # Run trading session
        print("🔄 Starting optimized trading session...")
        await trader.run_live_trading(duration_minutes=5)  # 5-minute test

        print("✅ Fixed live trading session completed successfully")
        return True

    except Exception as e:
        print(f"❌ Error in fixed live trading: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_fixed_live_trading())
    exit(0 if success else 1)
