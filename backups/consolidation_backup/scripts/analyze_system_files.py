#!/usr/bin/env python3
"""
Analyze System Files for Cleanup

Identifies conflicting configuration files and irrelevant root directory files.
"""

import os
import json
from pathlib import Path

def analyze_config_files():
    """Analyze configuration files for conflicts."""
    print("🔍 ANALYZING CONFIGURATION FILES")
    print("=" * 50)
    
    # Files actually used by our working system
    active_configs = {
        "config.yaml": "Main configuration file",
        "config/jupiter_config.yaml": "Jupiter swap configuration", 
        "config/token_registry.yaml": "Token registry for swaps",
        ".env": "Environment variables"
    }
    
    # Find all config files
    config_files = []
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(('.yaml', '.yml', '.json')) and 'config' in file.lower():
                config_files.append(os.path.join(root, file))
    
    print("✅ ACTIVE CONFIGURATION FILES:")
    for config, description in active_configs.items():
        if os.path.exists(config):
            print(f"   {config} - {description}")
        else:
            print(f"   ❌ MISSING: {config} - {description}")
    
    print("\n⚠️ POTENTIALLY CONFLICTING CONFIG FILES:")
    conflicting = []
    for config_file in config_files:
        if config_file.startswith('./'):
            config_file = config_file[2:]
        
        if config_file not in active_configs and not config_file.startswith('output/'):
            conflicting.append(config_file)
            print(f"   {config_file}")
    
    return conflicting

def analyze_root_directory():
    """Analyze root directory for irrelevant files."""
    print("\n🔍 ANALYZING ROOT DIRECTORY FILES")
    print("=" * 50)
    
    # Essential root files for our working system
    essential_files = {
        ".env", ".gitignore", "README.md", "requirements.txt",
        "config.yaml", "DEPLOYMENT_CHECKLIST.md", "depr.txt"
    }
    
    # Essential directories
    essential_dirs = {
        "scripts", "phase_4_deployment", "core", "output", "logs",
        "config", "wallet", ".git"
    }
    
    root_items = os.listdir(".")
    
    print("✅ ESSENTIAL ROOT FILES:")
    for item in sorted(root_items):
        if os.path.isfile(item) and item in essential_files:
            print(f"   {item}")
    
    print("\n✅ ESSENTIAL ROOT DIRECTORIES:")
    for item in sorted(root_items):
        if os.path.isdir(item) and item in essential_dirs:
            print(f"   {item}/")
    
    print("\n⚠️ POTENTIALLY IRRELEVANT ROOT FILES:")
    irrelevant_files = []
    for item in sorted(root_items):
        if os.path.isfile(item) and item not in essential_files:
            irrelevant_files.append(item)
            print(f"   {item}")
    
    print("\n⚠️ POTENTIALLY IRRELEVANT ROOT DIRECTORIES:")
    irrelevant_dirs = []
    for item in sorted(root_items):
        if os.path.isdir(item) and item not in essential_dirs:
            irrelevant_dirs.append(item)
            print(f"   {item}/")
    
    return irrelevant_files, irrelevant_dirs

def analyze_test_files():
    """Analyze test files for alignment."""
    print("\n🔍 ANALYZING TEST FILES")
    print("=" * 50)
    
    # Find all test files
    test_files = []
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.startswith('test_') and file.endswith('.py'):
                test_files.append(os.path.join(root, file))
    
    # Current working system components
    current_components = [
        "unified_live_trading", "telegram_notifier", "helius_client",
        "tx_builder", "transaction_executor", "dashboard", "trade_analyzer"
    ]
    
    print("✅ CURRENT TEST FILES:")
    aligned_tests = []
    for test_file in test_files:
        if any(component in test_file for component in current_components):
            aligned_tests.append(test_file)
            print(f"   {test_file}")
    
    print("\n⚠️ POTENTIALLY OUTDATED TEST FILES:")
    outdated_tests = []
    for test_file in test_files:
        if not any(component in test_file for component in current_components):
            outdated_tests.append(test_file)
            print(f"   {test_file}")
    
    return aligned_tests, outdated_tests

def generate_depr_list():
    """Generate list of files to add to depr.txt."""
    print("\n📝 GENERATING DEPRECATION LIST")
    print("=" * 50)
    
    conflicting_configs = analyze_config_files()
    irrelevant_files, irrelevant_dirs = analyze_root_directory()
    aligned_tests, outdated_tests = analyze_test_files()
    
    depr_candidates = []
    
    # Add conflicting configs
    for config in conflicting_configs:
        if not config.startswith('backups/') and not config.startswith('output/'):
            depr_candidates.append(f"{config} # Conflicting configuration file")
    
    # Add irrelevant root files (be selective)
    selective_irrelevant = []
    for file in irrelevant_files:
        if any(pattern in file.lower() for pattern in ['old', 'backup', 'temp', 'test', 'example']):
            selective_irrelevant.append(file)
    
    for file in selective_irrelevant:
        depr_candidates.append(f"{file} # Irrelevant root file")
    
    # Add irrelevant directories (be very selective)
    selective_dirs = []
    for dir_name in irrelevant_dirs:
        if any(pattern in dir_name.lower() for pattern in ['backup', 'old', 'temp', 'archive']):
            selective_dirs.append(dir_name)
    
    for dir_name in selective_dirs:
        depr_candidates.append(f"{dir_name}/ # Irrelevant directory")
    
    # Add outdated tests
    for test in outdated_tests:
        if not test.startswith('./backups/'):
            depr_candidates.append(f"{test} # Outdated test file")
    
    print("📋 CANDIDATES FOR DEPR.TXT:")
    for candidate in depr_candidates:
        print(f"   {candidate}")
    
    return depr_candidates

def save_analysis_report():
    """Save analysis report to file."""
    report = {
        "analysis_timestamp": "2025-05-25T00:45:00",
        "system_status": "fully_operational",
        "active_configs": [
            "config.yaml",
            "config/jupiter_config.yaml", 
            "config/token_registry.yaml",
            ".env"
        ],
        "essential_scripts": [
            "scripts/unified_live_trading.py",
            "scripts/execute_10_minute_session.py",
            "scripts/test_fixed_live_trading.py",
            "scripts/rich_trade_analyzer.py"
        ],
        "essential_components": [
            "phase_4_deployment/rpc_execution/",
            "phase_4_deployment/unified_dashboard/",
            "core/notifications/",
            "core/risk/"
        ],
        "deprecation_candidates": generate_depr_list()
    }
    
    os.makedirs("output/analysis", exist_ok=True)
    with open("output/analysis/system_analysis_report.json", 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n💾 Analysis report saved: output/analysis/system_analysis_report.json")
    return report

def main():
    """Main analysis function."""
    print("🔍 COMPREHENSIVE SYSTEM FILE ANALYSIS")
    print("=" * 60)
    print("Analyzing configuration files, root directory, and test alignment")
    print("=" * 60)
    
    # Run analysis
    report = save_analysis_report()
    
    print("\n" + "=" * 60)
    print("📊 ANALYSIS SUMMARY")
    print("=" * 60)
    print(f"✅ System Status: {report['system_status']}")
    print(f"📁 Active Configs: {len(report['active_configs'])}")
    print(f"🔧 Essential Scripts: {len(report['essential_scripts'])}")
    print(f"📦 Essential Components: {len(report['essential_components'])}")
    print(f"🗑️ Deprecation Candidates: {len(report['deprecation_candidates'])}")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Review deprecation candidates")
    print("2. Update depr.txt with selected items")
    print("3. Clean up conflicting configuration files")
    print("4. Align test suite with current system")
    print("5. Update README.md with current system manual")
    
    return 0

if __name__ == "__main__":
    exit(main())
