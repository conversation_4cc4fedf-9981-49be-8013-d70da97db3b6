#!/usr/bin/env python3
"""
Integration Test Script for Synergy7 Trading System

This script performs an integration test of the Synergy7 Trading System,
focusing on the new components added as part of the strategy_finder.md directives.
"""

import os
import sys
import logging
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import argparse

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules to test
from shared.utils.config_loader import get_config_loader, load_config
from phase_4_deployment.apis.helius_client import HeliusClient
from phase_4_deployment.rpc_execution.jito_client import JitoClient
from core.risk.position_sizer import PositionSizer
from core.risk.stop_loss import StopLossManager
from core.risk.portfolio_limits import PortfolioLimits
from core.risk.circuit_breaker import CircuitBreaker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("integration_test")

class IntegrationTest:
    """Integration test for the Synergy7 Trading System."""
    
    def __init__(self, config_path=None):
        """Initialize the integration test."""
        # Load configuration
        self.config_loader = get_config_loader()
        self.config = load_config(environment="development", components=["backtest"])
        
        # Initialize components
        self.helius_client = None
        self.jito_client = None
        self.position_sizer = None
        self.stop_loss_manager = None
        self.portfolio_limits = None
        self.circuit_breaker = None
        
        # Test data
        self.price_data = self._create_test_price_data()
        
        logger.info("Initialized integration test")
    
    def _create_test_price_data(self):
        """Create test price data."""
        # Create test price data
        dates = pd.date_range(start="2023-01-01", periods=100, freq="D")
        prices = np.linspace(100, 200, 100) + np.random.normal(0, 5, 100)
        
        # Add some momentum patterns
        for i in range(30, 40):
            prices[i] += i - 30
        for i in range(60, 70):
            prices[i] -= i - 60
        
        price_data = pd.DataFrame({
            "close": prices,
            "high": prices + np.random.normal(0, 1, 100),
            "low": prices - np.random.normal(0, 1, 100),
            "volume": np.random.normal(1000, 100, 100)
        }, index=dates)
        
        return price_data
    
    async def initialize_components(self):
        """Initialize all components."""
        # Initialize API clients
        self.helius_client = HeliusClient(config=self.config.get("apis", {}).get("helius", {}))
        self.jito_client = JitoClient()
        
        # Initialize risk management components
        self.position_sizer = PositionSizer(self.config.get("risk_management", {}))
        self.stop_loss_manager = StopLossManager(self.config.get("risk_management", {}))
        self.portfolio_limits = PortfolioLimits(self.config.get("risk_management", {}))
        self.circuit_breaker = CircuitBreaker(self.config.get("risk_management", {}))
        
        # Set initial balance
        self.portfolio_limits.set_initial_balance(10000)
        
        logger.info("Initialized all components")
    
    async def test_api_clients(self):
        """Test API clients."""
        logger.info("Testing API clients...")
        
        # Test Helius client
        try:
            blockhash = await self.helius_client.get_recent_blockhash()
            logger.info(f"Helius client returned blockhash: {blockhash}")
            
            # Test circuit breaker status
            circuit_breaker_status = self.helius_client.get_circuit_breaker_status()
            logger.info(f"Helius client circuit breaker status: {circuit_breaker_status}")
        except Exception as e:
            logger.error(f"Error testing Helius client: {str(e)}")
            return False
        
        # Test Jito client
        try:
            metrics = self.jito_client.get_metrics()
            logger.info(f"Jito client metrics: {metrics}")
        except Exception as e:
            logger.error(f"Error testing Jito client: {str(e)}")
            return False
        
        logger.info("API client tests passed")
        return True
    
    async def test_risk_management(self):
        """Test risk management components."""
        logger.info("Testing risk management components...")
        
        # Test position sizer
        try:
            position_size_result = self.position_sizer.calculate_position_size(
                self.price_data,
                10000,
                "SOL-USDC",
                0.8
            )
            logger.info(f"Position sizer result: {position_size_result}")
            
            stop_loss_result = self.position_sizer.calculate_stop_loss(
                150.0,
                0.05,
                10000,
                self.price_data,
                True
            )
            logger.info(f"Stop loss result: {stop_loss_result}")
            
            take_profit_price = self.position_sizer.calculate_take_profit(
                150.0,
                145.0,
                True,
                2.0
            )
            logger.info(f"Take profit price: {take_profit_price}")
        except Exception as e:
            logger.error(f"Error testing position sizer: {str(e)}")
            return False
        
        # Test stop loss manager
        try:
            entry_time = pd.Timestamp("2023-01-01 12:00:00")
            stop_info = self.stop_loss_manager.set_initial_stop(
                "trade1",
                100.0,
                entry_time,
                95.0,
                True
            )
            logger.info(f"Stop loss info: {stop_info}")
            
            current_time = entry_time + timedelta(hours=1)
            updated_stop_info = self.stop_loss_manager.update_stop(
                "trade1",
                102.0,
                current_time
            )
            logger.info(f"Updated stop loss info: {updated_stop_info}")
            
            triggered = self.stop_loss_manager.check_stop_triggered(
                "trade1",
                94.0
            )
            logger.info(f"Stop loss triggered: {triggered}")
        except Exception as e:
            logger.error(f"Error testing stop loss manager: {str(e)}")
            return False
        
        # Test portfolio limits
        try:
            self.portfolio_limits.add_position(
                "position1",
                "SOL-USDC",
                0.5,
                100.0,
                True
            )
            logger.info(f"Added position to portfolio limits")
            
            total_exposure = self.portfolio_limits.get_total_exposure()
            logger.info(f"Total exposure: {total_exposure}")
            
            limits_check = self.portfolio_limits.check_limits()
            logger.info(f"Limits check: {limits_check}")
            
            can_open, reason = self.portfolio_limits.can_open_position(
                "SOL-USDC",
                0.1,
                100.0
            )
            logger.info(f"Can open position: {can_open}, reason: {reason}")
        except Exception as e:
            logger.error(f"Error testing portfolio limits: {str(e)}")
            return False
        
        # Test circuit breaker
        try:
            self.circuit_breaker.update_balance(10000)
            logger.info(f"Updated circuit breaker balance")
            
            self.circuit_breaker.record_trade_result("trade1", 100, 10000)
            logger.info(f"Recorded trade result in circuit breaker")
            
            self.circuit_breaker.record_api_failure("helius")
            logger.info(f"Recorded API failure in circuit breaker")
            
            can_trade, reason = self.circuit_breaker.can_trade()
            logger.info(f"Can trade: {can_trade}, reason: {reason}")
            
            status = self.circuit_breaker.get_status()
            logger.info(f"Circuit breaker status: {status}")
        except Exception as e:
            logger.error(f"Error testing circuit breaker: {str(e)}")
            return False
        
        logger.info("Risk management tests passed")
        return True
    
    async def test_end_to_end(self):
        """Test end-to-end workflow."""
        logger.info("Testing end-to-end workflow...")
        
        # Simulate a trading cycle
        try:
            # 1. Check if trading is allowed
            can_trade, reason = self.circuit_breaker.can_trade()
            if not can_trade:
                logger.info(f"Trading not allowed: {reason}")
                return True
            
            # 2. Calculate position size
            position_size_result = self.position_sizer.calculate_position_size(
                self.price_data,
                10000,
                "SOL-USDC",
                0.8
            )
            position_size = position_size_result["position_size"]
            
            # 3. Check if position can be opened
            can_open, reason = self.portfolio_limits.can_open_position(
                "SOL-USDC",
                position_size,
                150.0
            )
            if not can_open:
                logger.info(f"Cannot open position: {reason}")
                return True
            
            # 4. Calculate stop loss and take profit
            stop_loss_result = self.position_sizer.calculate_stop_loss(
                150.0,
                position_size,
                10000,
                self.price_data,
                True
            )
            stop_loss_price = stop_loss_result["stop_loss_price"]
            
            take_profit_price = self.position_sizer.calculate_take_profit(
                150.0,
                stop_loss_price,
                True,
                2.0
            )
            
            # 5. Set initial stop loss
            entry_time = pd.Timestamp.now()
            stop_info = self.stop_loss_manager.set_initial_stop(
                "trade1",
                150.0,
                entry_time,
                stop_loss_price,
                True
            )
            
            # 6. Add position to portfolio
            self.portfolio_limits.add_position(
                "trade1",
                "SOL-USDC",
                position_size,
                150.0,
                True
            )
            
            # 7. Simulate price movement
            current_time = entry_time + timedelta(hours=1)
            current_price = 155.0
            
            # 8. Update stop loss
            updated_stop_info = self.stop_loss_manager.update_stop(
                "trade1",
                current_price,
                current_time
            )
            
            # 9. Check if stop loss is triggered
            triggered = self.stop_loss_manager.check_stop_triggered(
                "trade1",
                current_price
            )
            
            # 10. Update portfolio
            self.portfolio_limits.update_position(
                "trade1",
                current_price
            )
            
            # 11. Check portfolio limits
            limits_check = self.portfolio_limits.check_limits()
            
            # 12. Simulate trade completion
            profit_loss = (current_price - 150.0) * position_size
            self.circuit_breaker.record_trade_result("trade1", profit_loss, 10000)
            
            # 13. Remove position and stop loss
            self.portfolio_limits.remove_position("trade1")
            self.stop_loss_manager.remove_stop("trade1")
            
            # 14. Update balance
            new_balance = 10000 + profit_loss
            self.portfolio_limits.update_balance(new_balance)
            self.circuit_breaker.update_balance(new_balance)
            
            logger.info(f"End-to-end workflow completed successfully")
            logger.info(f"Profit/Loss: {profit_loss}")
            logger.info(f"New balance: {new_balance}")
        except Exception as e:
            logger.error(f"Error in end-to-end workflow: {str(e)}")
            return False
        
        logger.info("End-to-end tests passed")
        return True
    
    async def run_tests(self):
        """Run all tests."""
        logger.info("Starting integration tests...")
        
        # Initialize components
        await self.initialize_components()
        
        # Run tests
        api_test_result = await self.test_api_clients()
        risk_test_result = await self.test_risk_management()
        end_to_end_result = await self.test_end_to_end()
        
        # Clean up
        await self.helius_client.close()
        await self.jito_client.close()
        
        # Return results
        return {
            "api_test_result": api_test_result,
            "risk_test_result": risk_test_result,
            "end_to_end_result": end_to_end_result,
            "overall_result": api_test_result and risk_test_result and end_to_end_result
        }

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run integration tests for Synergy7 Trading System")
    parser.add_argument("--config", help="Path to configuration file")
    args = parser.parse_args()
    
    # Run integration tests
    integration_test = IntegrationTest(config_path=args.config)
    results = await integration_test.run_tests()
    
    # Print results
    logger.info("Integration test results:")
    logger.info(f"API test result: {'PASS' if results['api_test_result'] else 'FAIL'}")
    logger.info(f"Risk management test result: {'PASS' if results['risk_test_result'] else 'FAIL'}")
    logger.info(f"End-to-end test result: {'PASS' if results['end_to_end_result'] else 'FAIL'}")
    logger.info(f"Overall result: {'PASS' if results['overall_result'] else 'FAIL'}")
    
    # Return success/failure
    return results["overall_result"]

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
