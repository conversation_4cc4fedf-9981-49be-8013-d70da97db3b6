#!/usr/bin/env python3
"""
Update Deployment

This script updates the Q5 Trading System deployment.
"""

import os
import sys
import json
import logging
import argparse
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Optional, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DeploymentUpdater:
    """Updater for Q5 Trading System deployment."""
    
    def __init__(self):
        """Initialize the deployment updater."""
        self.deploy_dir = Path(__file__).parent.absolute()
        self.root_dir = self.deploy_dir.parent
        
        logger.info(f"Deploy directory: {self.deploy_dir}")
        logger.info(f"Root directory: {self.root_dir}")
    
    def backup_configuration(self) -> bool:
        """
        Backup configuration files.
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Backing up configuration files...")
        
        try:
            # Create backup directory
            backup_dir = self.root_dir / "backups" / datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Backup config.yaml
            config_file = self.root_dir / "config.yaml"
            if config_file.exists():
                backup_config_file = backup_dir / "config.yaml"
                with open(config_file, "r") as f_src:
                    with open(backup_config_file, "w") as f_dest:
                        f_dest.write(f_src.read())
                logger.info(f"Backed up configuration file to {backup_config_file}")
            
            # Backup .env
            env_file = self.root_dir / ".env"
            if env_file.exists():
                backup_env_file = backup_dir / ".env"
                with open(env_file, "r") as f_src:
                    with open(backup_env_file, "w") as f_dest:
                        f_dest.write(f_src.read())
                logger.info(f"Backed up environment file to {backup_env_file}")
            
            # Backup wallet keypair
            wallet_keypair_file = self.root_dir / "keys" / "wallet_keypair.json"
            if wallet_keypair_file.exists():
                backup_wallet_keypair_file = backup_dir / "wallet_keypair.json"
                with open(wallet_keypair_file, "r") as f_src:
                    with open(backup_wallet_keypair_file, "w") as f_dest:
                        f_dest.write(f_src.read())
                logger.info(f"Backed up wallet keypair file to {backup_wallet_keypair_file}")
            
            logger.info(f"Configuration files backed up to {backup_dir}")
            return True
        except Exception as e:
            logger.error(f"Error backing up configuration files: {str(e)}")
            return False
    
    def stop_deployment(self) -> bool:
        """
        Stop the deployment.
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Stopping deployment...")
        
        try:
            # Run deploy.py stop
            result = subprocess.run(
                [sys.executable, str(self.deploy_dir / "deploy.py"), "stop"],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Error stopping deployment: {result.stderr}")
                return False
            
            logger.info("Deployment stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Error stopping deployment: {str(e)}")
            return False
    
    def pull_latest_changes(self) -> bool:
        """
        Pull latest changes from the repository.
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Pulling latest changes...")
        
        try:
            # Check if git repository
            if not (self.root_dir / ".git").exists():
                logger.error("Not a git repository")
                return False
            
            # Pull latest changes
            result = subprocess.run(
                ["git", "pull"],
                cwd=self.root_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Error pulling latest changes: {result.stderr}")
                return False
            
            logger.info(f"Latest changes pulled: {result.stdout}")
            return True
        except Exception as e:
            logger.error(f"Error pulling latest changes: {str(e)}")
            return False
    
    def update_dependencies(self) -> bool:
        """
        Update dependencies.
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Updating dependencies...")
        
        try:
            # Check if requirements.txt exists
            requirements_file = self.root_dir / "requirements.txt"
            if not requirements_file.exists():
                logger.error(f"Requirements file not found: {requirements_file}")
                return False
            
            # Update dependencies
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Error updating dependencies: {result.stderr}")
                return False
            
            logger.info("Dependencies updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating dependencies: {str(e)}")
            return False
    
    def rebuild_docker_image(self) -> bool:
        """
        Rebuild the Docker image.
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Rebuilding Docker image...")
        
        try:
            # Run deploy.py build
            result = subprocess.run(
                [sys.executable, str(self.deploy_dir / "deploy.py"), "build"],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Error rebuilding Docker image: {result.stderr}")
                return False
            
            logger.info("Docker image rebuilt successfully")
            return True
        except Exception as e:
            logger.error(f"Error rebuilding Docker image: {str(e)}")
            return False
    
    def redeploy(self, mode: str = "paper") -> bool:
        """
        Redeploy the system.
        
        Args:
            mode: Deployment mode (live, paper, backtest, simulation)
            
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info(f"Redeploying system in {mode} mode...")
        
        try:
            # Run deploy.py deploy
            result = subprocess.run(
                [sys.executable, str(self.deploy_dir / "deploy.py"), "deploy", "--mode", mode],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Error redeploying system: {result.stderr}")
                return False
            
            logger.info("System redeployed successfully")
            return True
        except Exception as e:
            logger.error(f"Error redeploying system: {str(e)}")
            return False
    
    def update_deployment(self, mode: str = "paper", skip_git: bool = False, skip_dependencies: bool = False) -> bool:
        """
        Update the deployment.
        
        Args:
            mode: Deployment mode (live, paper, backtest, simulation)
            skip_git: Skip pulling latest changes
            skip_dependencies: Skip updating dependencies
            
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info(f"Updating deployment in {mode} mode...")
        
        # Backup configuration
        if not self.backup_configuration():
            logger.error("Failed to backup configuration")
            return False
        
        # Stop deployment
        if not self.stop_deployment():
            logger.error("Failed to stop deployment")
            return False
        
        # Pull latest changes
        if not skip_git:
            if not self.pull_latest_changes():
                logger.error("Failed to pull latest changes")
                return False
        
        # Update dependencies
        if not skip_dependencies:
            if not self.update_dependencies():
                logger.error("Failed to update dependencies")
                return False
        
        # Rebuild Docker image
        if not self.rebuild_docker_image():
            logger.error("Failed to rebuild Docker image")
            return False
        
        # Redeploy
        if not self.redeploy(mode):
            logger.error("Failed to redeploy system")
            return False
        
        logger.info("Deployment updated successfully")
        return True

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Update Q5 Trading System deployment")
    parser.add_argument("--mode", choices=["live", "paper", "backtest", "simulation"], default="paper", help="Deployment mode")
    parser.add_argument("--skip-git", action="store_true", help="Skip pulling latest changes")
    parser.add_argument("--skip-dependencies", action="store_true", help="Skip updating dependencies")
    
    args = parser.parse_args()
    
    # Create deployment updater
    updater = DeploymentUpdater()
    
    try:
        # Update deployment
        if not updater.update_deployment(args.mode, args.skip_git, args.skip_dependencies):
            logger.error("Failed to update deployment")
            return 1
        
        logger.info("Deployment updated successfully")
        return 0
    except Exception as e:
        logger.error(f"Error updating deployment: {str(e)}")
        return 1

if __name__ == "__main__":
    # Import datetime here to avoid circular import
    from datetime import datetime
    
    sys.exit(main())
