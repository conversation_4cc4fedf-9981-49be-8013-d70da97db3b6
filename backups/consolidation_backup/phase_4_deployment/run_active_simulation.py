#!/usr/bin/env python3
"""
Active Trading Simulation Script for the Q5 System.

This script runs an active trading simulation of the Q5 System with mock signals.
"""

import os
import sys
import time
import signal
import logging
import asyncio
import subprocess
import threading
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('active_simulation')

# Load simulation environment variables
def load_simulation_env():
    """Load simulation environment variables."""
    env_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env.active_simulation')
    if os.path.exists(env_file):
        logger.info(f"Loading environment variables from {env_file}")
        load_dotenv(env_file)
        return True
    else:
        logger.error(f"Environment file not found: {env_file}")
        return False

# Start the Streamlit dashboard
def start_streamlit_dashboard():
    """Start the Streamlit dashboard."""
    dashboard_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
        "monitoring",
        "streamlit_dashboard.py"
    )

    if not os.path.exists(dashboard_path):
        logger.error(f"Dashboard file not found: {dashboard_path}")
        return None

    logger.info(f"Starting Streamlit dashboard: {dashboard_path}")

    # Start the dashboard in a separate process
    process = subprocess.Popen(
        ["streamlit", "run", dashboard_path, "--server.port", "8501", "--server.headless", "false"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )

    logger.info(f"Streamlit dashboard started with PID {process.pid}")
    return process

# Start the live trading script
def start_live_trading():
    """Start the live trading script."""
    script_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
        "start_live_trading.py"
    )

    if not os.path.exists(script_path):
        logger.error(f"Live trading script not found: {script_path}")
        return None

    logger.info(f"Starting live trading script: {script_path}")

    # Start the live trading script in a separate process
    process = subprocess.Popen(
        [sys.executable, script_path],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        env=os.environ.copy()
    )

    logger.info(f"Live trading script started with PID {process.pid}")
    return process

# Start the mock signal generator
def start_mock_signal_generator():
    """Start the mock signal generator."""
    # Add the current directory to the path
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))

    # Import the mock signal generator
    try:
        from utils.mock_signal_generator import MockSignalGenerator
    except ImportError:
        logger.error("Failed to import MockSignalGenerator")
        return None

    logger.info("Starting mock signal generator")

    # Create a mock signal generator
    generator = MockSignalGenerator()

    # Get the interval from environment
    interval = float(os.environ.get('SIMULATION_MOCK_SIGNAL_INTERVAL', '10'))

    # Start generating signals in a separate thread
    def generate_signals():
        asyncio.run(generator.generate_signals_periodically(interval_seconds=interval))

    thread = threading.Thread(target=generate_signals, daemon=True)
    thread.start()

    logger.info(f"Mock signal generator started with interval {interval} seconds")
    return thread

# Monitor the log file for trading activity
def monitor_log_file(log_file_path, callback):
    """
    Monitor a log file for new lines.

    Args:
        log_file_path: Path to the log file
        callback: Callback function to call with each new line
    """
    # Wait for the log file to be created
    while not os.path.exists(log_file_path):
        time.sleep(0.5)

    # Open the log file
    with open(log_file_path, 'r') as f:
        # Seek to the end of the file
        f.seek(0, 2)

        # Monitor for new lines
        while True:
            line = f.readline()
            if line:
                callback(line)
            else:
                time.sleep(0.1)

# Find the most recent log file
def find_most_recent_log_file():
    """
    Find the most recent log file.

    Returns:
        Path to the most recent log file
    """
    log_dir = os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
        "output",
        "live_trading_logs"
    )

    if not os.path.exists(log_dir):
        logger.error(f"Log directory not found: {log_dir}")
        return None

    # Get all log files
    log_files = [os.path.join(log_dir, f) for f in os.listdir(log_dir) if f.startswith('live_trading_') and f.endswith('.log')]

    if not log_files:
        logger.error("No log files found")
        return None

    # Sort by modification time (newest first)
    log_files.sort(key=os.path.getmtime, reverse=True)

    return log_files[0]

# Stop a process
def stop_process(process, name):
    """Stop a process."""
    if process:
        logger.info(f"Stopping {name} (PID {process.pid})...")
        process.terminate()
        try:
            process.wait(timeout=5)
            logger.info(f"{name} stopped")
        except subprocess.TimeoutExpired:
            logger.warning(f"{name} did not terminate, killing it")
            process.kill()
            process.wait()
            logger.info(f"{name} killed")

# Run the active simulation
def run_active_simulation(duration_seconds=60):
    """
    Run the active simulation.

    Args:
        duration_seconds: Duration of the simulation in seconds
    """
    logger.info(f"Starting active trading simulation for {duration_seconds} seconds")

    # Load simulation environment variables
    if not load_simulation_env():
        logger.error("Failed to load simulation environment variables")
        return False

    # Start the Streamlit dashboard
    dashboard_process = start_streamlit_dashboard()

    # Start the mock signal generator
    signal_generator_thread = start_mock_signal_generator()

    # Start the live trading script
    trading_process = start_live_trading()

    # Wait for a bit to let the system initialize
    time.sleep(5)

    # Find the most recent log file
    log_file_path = find_most_recent_log_file()
    if log_file_path:
        logger.info(f"Monitoring log file: {log_file_path}")

        # Start monitoring the log file in a separate thread
        def log_callback(line):
            if "Building transaction from signal" in line or "Executing transaction" in line:
                logger.info(f"Trading activity detected: {line.strip()}")

        log_monitor_thread = threading.Thread(
            target=monitor_log_file,
            args=(log_file_path, log_callback),
            daemon=True
        )
        log_monitor_thread.start()

    # Wait for the specified duration
    try:
        logger.info(f"Active simulation running for {duration_seconds} seconds...")
        time.sleep(duration_seconds)
    except KeyboardInterrupt:
        logger.info("Simulation interrupted by user")

    # Stop the processes
    stop_process(trading_process, "live trading script")
    stop_process(dashboard_process, "Streamlit dashboard")

    logger.info("Active simulation completed")
    return True

# Main function
def main():
    """Main function."""
    import argparse

    parser = argparse.ArgumentParser(description='Run an active trading simulation of the Q5 System')
    parser.add_argument('--duration', '-d', type=int, default=60,
                        help='Duration of the simulation in seconds (default: 60)')
    args = parser.parse_args()

    success = run_active_simulation(args.duration)
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
