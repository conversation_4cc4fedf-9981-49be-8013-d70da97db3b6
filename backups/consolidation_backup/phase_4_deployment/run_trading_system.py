#!/usr/bin/env python3
"""
Run Trading System

This script runs all components of the trading system.
"""

import os
import sys
import json
import time
import logging
import asyncio
import subprocess
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, Callable, Awaitable

# Install required packages
try:
    import yaml
except ImportError:
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyyaml"])
    import yaml

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import components
from phase_4_deployment.python_comm_layer.mock_carbon_core import MockCarbonCore
from phase_4_deployment.stream_data_ingestor.sources import HeliusDataSource, BirdeyeDataSource, JitoDataSource
from phase_4_deployment.signal_generator.signal_generator import SignalGenerator
from phase_4_deployment.strategy_runner.strategy_runner import <PERSON><PERSON><PERSON><PERSON>
from phase_4_deployment.risk_management.risk_manager import RiskManager
from phase_4_deployment.transaction_preparation.transaction_preparer import TransactionPreparer
from phase_4_deployment.transaction_execution.transaction_executor import TransactionExecutor
from phase_4_deployment.monitoring.health_check_server import HealthCheckServer
from phase_4_deployment.monitoring.telegram_alerts import TelegramAlerts

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TradingSystem:
    """Trading system."""

    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the trading system.

        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path

        # Load configuration
        self.config = self._load_config()

        # Initialize components
        self.carbon_core = None
        self.signal_generator = None
        self.strategy_runner = None
        self.risk_manager = None
        self.transaction_preparer = None
        self.transaction_executor = None
        self.health_check_server = None
        self.telegram_alerts = None

        # Initialize state
        self.running = False

        logger.info("Initialized trading system")

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from file.

        Returns:
            Dict[str, Any]: Configuration
        """
        try:
            with open(self.config_path, "r") as f:
                config = yaml.safe_load(f)

            logger.info(f"Loaded configuration from {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            return {}

    async def start(self):
        """Start the trading system."""
        if self.running:
            logger.warning("Trading system is already running")
            return

        logger.info("Starting trading system...")

        # Start Carbon Core
        self.carbon_core = MockCarbonCore(
            pub_endpoint="tcp://127.0.0.1:5555",
            sub_endpoint="tcp://127.0.0.1:5556",
            req_endpoint="tcp://127.0.0.1:5557",
            config_path=self.config_path,
        )
        await self.carbon_core.start()
        logger.info("Started Carbon Core")

        # Start Signal Generator
        self.signal_generator = SignalGenerator(
            config=self.config,
            carbon_core_pub_endpoint="tcp://127.0.0.1:5556",
            carbon_core_sub_endpoint="tcp://127.0.0.1:5555",
            carbon_core_req_endpoint="tcp://127.0.0.1:5557",
        )
        await self.signal_generator.start()
        logger.info("Started Signal Generator")

        # Start Strategy Runner
        self.strategy_runner = StrategyRunner(
            config=self.config,
            carbon_core_pub_endpoint="tcp://127.0.0.1:5556",
            carbon_core_sub_endpoint="tcp://127.0.0.1:5555",
            carbon_core_req_endpoint="tcp://127.0.0.1:5557",
        )
        await self.strategy_runner.start()
        logger.info("Started Strategy Runner")

        # Start Risk Manager
        self.risk_manager = RiskManager(
            config=self.config,
            carbon_core_pub_endpoint="tcp://127.0.0.1:5556",
            carbon_core_sub_endpoint="tcp://127.0.0.1:5555",
            carbon_core_req_endpoint="tcp://127.0.0.1:5557",
        )
        await self.risk_manager.start()
        logger.info("Started Risk Manager")

        # Start Transaction Preparer
        self.transaction_preparer = TransactionPreparer(
            config=self.config,
            carbon_core_pub_endpoint="tcp://127.0.0.1:5556",
            carbon_core_sub_endpoint="tcp://127.0.0.1:5555",
            carbon_core_req_endpoint="tcp://127.0.0.1:5557",
        )
        await self.transaction_preparer.start()
        logger.info("Started Transaction Preparer")

        # Start Transaction Executor
        self.transaction_executor = TransactionExecutor(
            config=self.config,
            carbon_core_pub_endpoint="tcp://127.0.0.1:5556",
            carbon_core_sub_endpoint="tcp://127.0.0.1:5555",
            carbon_core_req_endpoint="tcp://127.0.0.1:5557",
        )
        await self.transaction_executor.start()
        logger.info("Started Transaction Executor")

        # Start Health Check Server
        self.health_check_server = HealthCheckServer(
            config=self.config,
            carbon_core_pub_endpoint="tcp://127.0.0.1:5556",
            carbon_core_sub_endpoint="tcp://127.0.0.1:5555",
            carbon_core_req_endpoint="tcp://127.0.0.1:5557",
            host="0.0.0.0",
            port=8080,
        )
        await self.health_check_server.start()
        logger.info("Started Health Check Server")

        # Start Telegram Alerts
        self.telegram_alerts = TelegramAlerts(
            config=self.config,
            carbon_core_pub_endpoint="tcp://127.0.0.1:5556",
            carbon_core_sub_endpoint="tcp://127.0.0.1:5555",
            carbon_core_req_endpoint="tcp://127.0.0.1:5557",
        )
        await self.telegram_alerts.start()
        logger.info("Started Telegram Alerts")

        # Set running flag
        self.running = True

        logger.info("Trading system started")

    async def stop(self):
        """Stop the trading system."""
        if not self.running:
            logger.warning("Trading system is not running")
            return

        logger.info("Stopping trading system...")

        # Stop Transaction Executor
        if self.transaction_executor:
            await self.transaction_executor.stop()
            logger.info("Stopped Transaction Executor")

        # Stop Transaction Preparer
        if self.transaction_preparer:
            await self.transaction_preparer.stop()
            logger.info("Stopped Transaction Preparer")

        # Stop Risk Manager
        if self.risk_manager:
            await self.risk_manager.stop()
            logger.info("Stopped Risk Manager")

        # Stop Strategy Runner
        if self.strategy_runner:
            await self.strategy_runner.stop()
            logger.info("Stopped Strategy Runner")

        # Stop Signal Generator
        if self.signal_generator:
            await self.signal_generator.stop()
            logger.info("Stopped Signal Generator")

        # Stop Carbon Core
        if self.carbon_core:
            await self.carbon_core.stop()
            logger.info("Stopped Carbon Core")

        # Stop Telegram Alerts
        if self.telegram_alerts:
            await self.telegram_alerts.stop()
            logger.info("Stopped Telegram Alerts")

        # Stop Health Check Server
        if self.health_check_server:
            await self.health_check_server.stop()
            logger.info("Stopped Health Check Server")

        # Set running flag
        self.running = False

        logger.info("Trading system stopped")

async def main():
    """Main function."""
    # Parse command line arguments
    import argparse

    parser = argparse.ArgumentParser(description="Run trading system")
    parser.add_argument("--config", default="config.yaml", help="Path to configuration file")

    args = parser.parse_args()

    # Create trading system
    trading_system = TradingSystem(config_path=args.config)

    try:
        # Start trading system
        await trading_system.start()

        # Run until interrupted
        logger.info("Press Ctrl+C to stop")
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    finally:
        # Stop trading system
        await trading_system.stop()

if __name__ == "__main__":
    asyncio.run(main())
