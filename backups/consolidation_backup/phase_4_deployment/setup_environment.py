#!/usr/bin/env python3
"""
Setup Environment

This script helps users set up their environment for deploying the Q5 Trading System.
"""

import os
import sys
import json
import logging
import argparse
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Optional, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnvironmentSetup:
    """Setup environment for Q5 Trading System deployment."""
    
    def __init__(self):
        """Initialize the environment setup."""
        self.deploy_dir = Path(__file__).parent.absolute()
        self.root_dir = self.deploy_dir.parent
        
        logger.info(f"Deploy directory: {self.deploy_dir}")
        logger.info(f"Root directory: {self.root_dir}")
    
    def check_prerequisites(self) -> bool:
        """
        Check if all prerequisites are met.
        
        Returns:
            bool: True if all prerequisites are met, False otherwise
        """
        logger.info("Checking prerequisites...")
        
        # Check Python version
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 9):
            logger.error(f"Python 3.9 or later is required. Found: {python_version.major}.{python_version.minor}")
            return False
        logger.info(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # Check if Docker is installed
        try:
            result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"Docker installed: {result.stdout.strip()}")
            else:
                logger.error("Docker is not installed or not in PATH")
                return False
        except FileNotFoundError:
            logger.error("Docker is not installed or not in PATH")
            return False
        
        # Check if Docker Compose is installed
        try:
            result = subprocess.run(["docker-compose", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"Docker Compose installed: {result.stdout.strip()}")
            else:
                logger.error("Docker Compose is not installed or not in PATH")
                return False
        except FileNotFoundError:
            logger.error("Docker Compose is not installed or not in PATH")
            return False
        
        # Check if git is installed
        try:
            result = subprocess.run(["git", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"Git installed: {result.stdout.strip()}")
            else:
                logger.error("Git is not installed or not in PATH")
                return False
        except FileNotFoundError:
            logger.error("Git is not installed or not in PATH")
            return False
        
        # Check disk space
        try:
            import shutil
            total, used, free = shutil.disk_usage(self.root_dir)
            total_gb = total / (1024 ** 3)
            free_gb = free / (1024 ** 3)
            logger.info(f"Disk space: {total_gb:.2f} GB total, {free_gb:.2f} GB free")
            if free_gb < 10:
                logger.warning(f"Low disk space: {free_gb:.2f} GB free. At least 10 GB recommended.")
        except Exception as e:
            logger.warning(f"Could not check disk space: {str(e)}")
        
        # Check memory
        try:
            import psutil
            memory = psutil.virtual_memory()
            total_gb = memory.total / (1024 ** 3)
            available_gb = memory.available / (1024 ** 3)
            logger.info(f"Memory: {total_gb:.2f} GB total, {available_gb:.2f} GB available")
            if available_gb < 4:
                logger.warning(f"Low memory: {available_gb:.2f} GB available. At least 4 GB recommended.")
        except ImportError:
            logger.warning("Could not check memory: psutil not installed")
        except Exception as e:
            logger.warning(f"Could not check memory: {str(e)}")
        
        # Check CPU cores
        try:
            import psutil
            cpu_count = psutil.cpu_count(logical=True)
            logger.info(f"CPU cores: {cpu_count}")
            if cpu_count < 2:
                logger.warning(f"Low CPU cores: {cpu_count}. At least 2 cores recommended.")
        except ImportError:
            logger.warning("Could not check CPU cores: psutil not installed")
        except Exception as e:
            logger.warning(f"Could not check CPU cores: {str(e)}")
        
        logger.info("All prerequisites checked")
        return True
    
    def setup_directories(self) -> bool:
        """
        Set up directories for deployment.
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Setting up directories...")
        
        try:
            # Create directories
            directories = [
                self.root_dir / "keys",
                self.root_dir / "logs",
                self.root_dir / "output",
                self.root_dir / "phase_0_env_setup" / "data" / "historical",
                self.root_dir / "phase_0_env_setup" / "data" / "raw_events",
                self.root_dir / "phase_1_strategy_runner" / "outputs",
                self.root_dir / "phase_2_backtest_engine" / "output",
                self.root_dir / "phase_3_rl_agent_training" / "output",
                self.root_dir / "phase_4_deployment" / "wallet_sync" / "data",
            ]
            
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created directory: {directory}")
            
            logger.info("Directories set up successfully")
            return True
        except Exception as e:
            logger.error(f"Error setting up directories: {str(e)}")
            return False
    
    def setup_environment_file(self) -> bool:
        """
        Set up environment file for deployment.
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Setting up environment file...")
        
        try:
            # Check if .env file already exists
            env_file = self.root_dir / ".env"
            if env_file.exists():
                logger.info(f"Environment file already exists: {env_file}")
                return True
            
            # Copy sample.env to .env
            sample_env_file = self.deploy_dir / "sample.env"
            if not sample_env_file.exists():
                logger.error(f"Sample environment file not found: {sample_env_file}")
                return False
            
            with open(sample_env_file, "r") as f_src:
                with open(env_file, "w") as f_dest:
                    f_dest.write(f_src.read())
            
            logger.info(f"Environment file created: {env_file}")
            logger.info("Please edit the .env file with your API keys and settings")
            return True
        except Exception as e:
            logger.error(f"Error setting up environment file: {str(e)}")
            return False
    
    def setup_configuration_file(self) -> bool:
        """
        Set up configuration file for deployment.
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Setting up configuration file...")
        
        try:
            # Check if config.yaml file already exists
            config_file = self.root_dir / "config.yaml"
            if config_file.exists():
                logger.info(f"Configuration file already exists: {config_file}")
                return True
            
            # Copy production_config.yaml to config.yaml
            production_config_file = self.deploy_dir / "production_config.yaml"
            if not production_config_file.exists():
                logger.error(f"Production configuration file not found: {production_config_file}")
                return False
            
            with open(production_config_file, "r") as f_src:
                with open(config_file, "w") as f_dest:
                    f_dest.write(f_src.read())
            
            logger.info(f"Configuration file created: {config_file}")
            logger.info("Please review and update the configuration file with your settings")
            return True
        except Exception as e:
            logger.error(f"Error setting up configuration file: {str(e)}")
            return False
    
    def generate_wallet(self) -> bool:
        """
        Generate wallet keypair for deployment.
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Generating wallet keypair...")
        
        try:
            # Check if wallet keypair already exists
            wallet_keypair_file = self.root_dir / "keys" / "wallet_keypair.json"
            if wallet_keypair_file.exists():
                logger.info(f"Wallet keypair already exists: {wallet_keypair_file}")
                return True
            
            # Run generate_test_wallet.py
            generate_wallet_script = self.deploy_dir / "utils" / "generate_test_wallet.py"
            if not generate_wallet_script.exists():
                logger.error(f"Wallet generation script not found: {generate_wallet_script}")
                return False
            
            result = subprocess.run(
                [sys.executable, str(generate_wallet_script), "--output", str(wallet_keypair_file)],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Error generating wallet keypair: {result.stderr}")
                return False
            
            logger.info(f"Wallet keypair generated: {wallet_keypair_file}")
            logger.info(result.stdout)
            return True
        except Exception as e:
            logger.error(f"Error generating wallet keypair: {str(e)}")
            return False
    
    def install_dependencies(self) -> bool:
        """
        Install dependencies for deployment.
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Installing dependencies...")
        
        try:
            # Check if requirements.txt exists
            requirements_file = self.root_dir / "requirements.txt"
            if not requirements_file.exists():
                logger.error(f"Requirements file not found: {requirements_file}")
                return False
            
            # Install dependencies
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Error installing dependencies: {result.stderr}")
                return False
            
            logger.info("Dependencies installed successfully")
            return True
        except Exception as e:
            logger.error(f"Error installing dependencies: {str(e)}")
            return False
    
    def setup_environment(self) -> bool:
        """
        Set up environment for deployment.
        
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Setting up environment...")
        
        # Check prerequisites
        if not self.check_prerequisites():
            logger.error("Prerequisites not met")
            return False
        
        # Set up directories
        if not self.setup_directories():
            logger.error("Failed to set up directories")
            return False
        
        # Set up environment file
        if not self.setup_environment_file():
            logger.error("Failed to set up environment file")
            return False
        
        # Set up configuration file
        if not self.setup_configuration_file():
            logger.error("Failed to set up configuration file")
            return False
        
        # Generate wallet
        if not self.generate_wallet():
            logger.error("Failed to generate wallet")
            return False
        
        # Install dependencies
        if not self.install_dependencies():
            logger.error("Failed to install dependencies")
            return False
        
        logger.info("Environment setup completed successfully")
        return True

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Setup environment for Q5 Trading System deployment")
    parser.add_argument("--skip-dependencies", action="store_true", help="Skip installing dependencies")
    
    args = parser.parse_args()
    
    # Create environment setup
    setup = EnvironmentSetup()
    
    try:
        # Check prerequisites
        if not setup.check_prerequisites():
            logger.error("Prerequisites not met")
            return 1
        
        # Set up directories
        if not setup.setup_directories():
            logger.error("Failed to set up directories")
            return 1
        
        # Set up environment file
        if not setup.setup_environment_file():
            logger.error("Failed to set up environment file")
            return 1
        
        # Set up configuration file
        if not setup.setup_configuration_file():
            logger.error("Failed to set up configuration file")
            return 1
        
        # Generate wallet
        if not setup.generate_wallet():
            logger.error("Failed to generate wallet")
            return 1
        
        # Install dependencies
        if not args.skip_dependencies:
            if not setup.install_dependencies():
                logger.error("Failed to install dependencies")
                return 1
        
        logger.info("Environment setup completed successfully")
        logger.info("Next steps:")
        logger.info("1. Edit the .env file with your API keys and settings")
        logger.info("2. Review and update the configuration file with your settings")
        logger.info("3. Fund your wallet with SOL")
        logger.info("4. Run 'python phase_4_deployment/deploy.py build' to build the Docker image")
        logger.info("5. Run 'python phase_4_deployment/deploy.py deploy --mode paper' to deploy in paper trading mode")
        
        return 0
    except Exception as e:
        logger.error(f"Error setting up environment: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
