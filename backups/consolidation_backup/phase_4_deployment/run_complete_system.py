#!/usr/bin/env python3
"""
Run Complete System

This script runs the complete trading system, including the trading components and the monitoring components.
"""

import os
import sys
import subprocess
import logging
import time
import argparse
import signal
import threading

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables
processes = []

def signal_handler(sig, frame):
    """
    Signal handler for graceful shutdown.
    
    Args:
        sig: Signal number
        frame: Current stack frame
    """
    logger.info("Received signal to stop")
    stop_all_processes()
    sys.exit(0)

def stop_all_processes():
    """Stop all processes."""
    logger.info("Stopping all processes...")
    
    for process in processes:
        if process.poll() is None:
            logger.info(f"Stopping process: {process.args}")
            process.terminate()
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning(f"Process did not terminate gracefully: {process.args}")
                process.kill()
    
    logger.info("All processes stopped")

def run_process(command, cwd=None):
    """
    Run a process.
    
    Args:
        command: Command to run
        cwd: Working directory
        
    Returns:
        subprocess.Popen: Process
    """
    logger.info(f"Running command: {command}")
    
    process = subprocess.Popen(
        command,
        cwd=cwd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True,
        bufsize=1,
    )
    
    processes.append(process)
    
    # Start threads to read stdout and stderr
    threading.Thread(target=read_output, args=(process.stdout, command[0], "stdout"), daemon=True).start()
    threading.Thread(target=read_output, args=(process.stderr, command[0], "stderr"), daemon=True).start()
    
    return process

def read_output(pipe, name, stream_name):
    """
    Read output from a pipe.
    
    Args:
        pipe: Pipe to read from
        name: Process name
        stream_name: Stream name
    """
    for line in pipe:
        logger.info(f"{name} ({stream_name}): {line.strip()}")

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run complete trading system")
    parser.add_argument("--config", default="config.yaml", help="Path to configuration file")
    parser.add_argument("--no-dashboard", action="store_true", help="Do not run the Streamlit dashboard")
    
    args = parser.parse_args()
    
    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Get the path to the scripts
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Run the trading system
        trading_system_script = os.path.join(script_dir, "run_trading_system.py")
        trading_system_process = run_process([
            sys.executable,
            trading_system_script,
            "--config",
            args.config,
        ])
        
        # Wait for the trading system to start
        logger.info("Waiting for the trading system to start...")
        time.sleep(5)
        
        # Run the Streamlit dashboard
        if not args.no_dashboard:
            streamlit_script = os.path.join(script_dir, "run_streamlit_dashboard.py")
            streamlit_process = run_process([
                sys.executable,
                streamlit_script,
            ])
        
        # Wait for processes to finish
        logger.info("All processes started. Press Ctrl+C to stop.")
        
        while True:
            # Check if any process has terminated
            for process in processes:
                if process.poll() is not None:
                    logger.error(f"Process terminated unexpectedly: {process.args}")
                    stop_all_processes()
                    return 1
            
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        stop_all_processes()
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        stop_all_processes()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
