#!/usr/bin/env python3
"""
Deploy Synergy7 Trading System

This script deploys the Synergy7 Trading System to a production environment.
"""

import os
import sys
import json
import time
import logging
import argparse
import subprocess
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Deployer:
    """Deployer for the Synergy7 Trading System."""

    def __init__(self, config_path: str = "production_config.yaml"):
        """
        Initialize the deployer.

        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path
        self.deploy_dir = os.path.dirname(os.path.abspath(__file__))
        self.root_dir = os.path.dirname(os.path.dirname(self.deploy_dir))

        logger.info(f"Initialized deployer with config path: {config_path}")
        logger.info(f"Deploy directory: {self.deploy_dir}")
        logger.info(f"Root directory: {self.root_dir}")

    def check_prerequisites(self) -> bool:
        """
        Check if all prerequisites are met.

        Returns:
            bool: True if all prerequisites are met, False otherwise
        """
        logger.info("Checking prerequisites...")

        # Check if Docker is installed
        try:
            subprocess.check_call(["docker", "--version"], stdout=subprocess.DEVNULL)
            logger.info("Docker is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("Docker is not installed")
            return False

        # Check if Docker Compose is installed
        try:
            subprocess.check_call(["docker-compose", "--version"], stdout=subprocess.DEVNULL)
            logger.info("Docker Compose is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("Docker Compose is not installed")
            return False

        # Check if configuration file exists
        if not os.path.exists(os.path.join(self.deploy_dir, self.config_path)):
            logger.error(f"Configuration file not found: {self.config_path}")
            return False

        # Check if Docker Compose file exists
        if not os.path.exists(os.path.join(self.deploy_dir, "docker-compose.yml")):
            logger.error("Docker Compose file not found")
            return False

        # Check if Dockerfile exists
        if not os.path.exists(os.path.join(self.deploy_dir, "docker_deploy", "Dockerfile")):
            logger.error("Dockerfile not found")
            return False

        logger.info("All prerequisites are met")
        return True

    def build_docker_image(self) -> bool:
        """
        Build the Docker image.

        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Building Docker image...")

        try:
            # Build the Docker image
            subprocess.check_call([
                "docker-compose",
                "-f", os.path.join(self.deploy_dir, "docker-compose.yml"),
                "build",
            ])

            logger.info("Docker image built successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error building Docker image: {str(e)}")
            return False

    def deploy(self, mode: str = "paper") -> bool:
        """
        Deploy the Synergy7 Trading System.

        Args:
            mode: Deployment mode (live, paper, backtest, simulation)

        Returns:
            bool: True if successful, False otherwise
        """
        logger.info(f"Deploying Synergy7 Trading System in {mode} mode...")

        try:
            # Set environment variables
            env = os.environ.copy()
            env["TRADING_MODE"] = mode

            if mode == "live":
                env["TRADING_ENABLED"] = "true"
                env["PAPER_TRADING"] = "false"
                env["BACKTESTING_ENABLED"] = "false"
                env["DRY_RUN"] = "false"
            elif mode == "paper":
                env["TRADING_ENABLED"] = "true"
                env["PAPER_TRADING"] = "true"
                env["BACKTESTING_ENABLED"] = "false"
                env["DRY_RUN"] = "true"
            elif mode == "backtest":
                env["TRADING_ENABLED"] = "false"
                env["PAPER_TRADING"] = "false"
                env["BACKTESTING_ENABLED"] = "true"
                env["DRY_RUN"] = "true"
            elif mode == "simulation":
                env["TRADING_ENABLED"] = "false"
                env["PAPER_TRADING"] = "true"
                env["BACKTESTING_ENABLED"] = "false"
                env["DRY_RUN"] = "true"

            # Copy configuration file
            config_dest = os.path.join(self.root_dir, "config.yaml")
            config_src = os.path.join(self.deploy_dir, self.config_path)

            logger.info(f"Copying configuration file from {config_src} to {config_dest}")

            with open(config_src, "r") as f_src:
                with open(config_dest, "w") as f_dest:
                    f_dest.write(f_src.read())

            # Start the containers
            subprocess.check_call([
                "docker-compose",
                "-f", os.path.join(self.deploy_dir, "docker-compose.yml"),
                "up",
                "-d",
            ], env=env)

            logger.info("Synergy7 Trading System deployed successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error deploying Synergy7 Trading System: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error deploying Synergy7 Trading System: {str(e)}")
            return False

    def stop(self) -> bool:
        """
        Stop the Synergy7 Trading System.

        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Stopping Synergy7 Trading System...")

        try:
            # Stop the containers
            subprocess.check_call([
                "docker-compose",
                "-f", os.path.join(self.deploy_dir, "docker-compose.yml"),
                "down",
            ])

            logger.info("Synergy7 Trading System stopped successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error stopping Synergy7 Trading System: {str(e)}")
            return False

    def logs(self, service: Optional[str] = None, follow: bool = False) -> bool:
        """
        Show logs for the Synergy7 Trading System.

        Args:
            service: Service to show logs for (None for all services)
            follow: Whether to follow the logs

        Returns:
            bool: True if successful, False otherwise
        """
        logger.info(f"Showing logs for Synergy7 Trading System{' (' + service + ')' if service else ''}...")

        try:
            # Build command
            cmd = [
                "docker-compose",
                "-f", os.path.join(self.deploy_dir, "docker-compose.yml"),
                "logs",
            ]

            if follow:
                cmd.append("-f")

            if service:
                cmd.append(service)

            # Show logs
            subprocess.check_call(cmd)

            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error showing logs: {str(e)}")
            return False

    def status(self) -> bool:
        """
        Show status of the Synergy7 Trading System.

        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("Showing status of Synergy7 Trading System...")

        try:
            # Show status
            subprocess.check_call([
                "docker-compose",
                "-f", os.path.join(self.deploy_dir, "docker-compose.yml"),
                "ps",
            ])

            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error showing status: {str(e)}")
            return False

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Deploy Synergy7 Trading System")
    parser.add_argument("--config", default="production_config.yaml", help="Path to configuration file")
    parser.add_argument("--mode", choices=["live", "paper", "backtest", "simulation"], default="paper", help="Deployment mode")
    parser.add_argument("command", choices=["deploy", "stop", "logs", "status", "build"], help="Command to execute")
    parser.add_argument("--service", help="Service to show logs for")
    parser.add_argument("--follow", action="store_true", help="Follow logs")

    args = parser.parse_args()

    # Create deployer
    deployer = Deployer(config_path=args.config)

    # Check prerequisites
    if not deployer.check_prerequisites():
        logger.error("Prerequisites not met")
        return 1

    # Execute command
    if args.command == "deploy":
        if not deployer.build_docker_image():
            logger.error("Failed to build Docker image")
            return 1

        if not deployer.deploy(mode=args.mode):
            logger.error("Failed to deploy Synergy7 Trading System")
            return 1
    elif args.command == "stop":
        if not deployer.stop():
            logger.error("Failed to stop Synergy7 Trading System")
            return 1
    elif args.command == "logs":
        if not deployer.logs(service=args.service, follow=args.follow):
            logger.error("Failed to show logs")
            return 1
    elif args.command == "status":
        if not deployer.status():
            logger.error("Failed to show status")
            return 1
    elif args.command == "build":
        if not deployer.build_docker_image():
            logger.error("Failed to build Docker image")
            return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
