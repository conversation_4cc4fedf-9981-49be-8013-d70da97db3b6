#!/usr/bin/env python3
"""
Test Trade Script

This script executes a small test trade to verify the end-to-end functionality
of the Q5 System with Helius RPC integration.
"""

import os
import sys
import json
import asyncio
import logging
from pathlib import Path
from datetime import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rpc_execution.helius_client import HeliusClient
from rpc_execution.helius_executor import HeliusExecutor
from rpc_execution.tx_builder import TxBuilder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_trade')

async def execute_test_trade():
    """Execute a small test trade."""
    # Get wallet address from environment
    wallet_address = os.getenv("WALLET_ADDRESS")
    if not wallet_address:
        logger.error("WALLET_ADDRESS environment variable not set")
        return False

    # Create output directory if it doesn't exist
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)

    # Initialize components
    logger.info("Initializing components...")

    # Create transaction builder
    builder = TxBuilder(wallet_address)

    # Create Helius executor
    executor = HeliusExecutor()

    # Create a small test signal (SOL to USDC swap)
    # Using a very small amount for testing
    test_signal = {
        "action": "BUY",
        "market": "SOL-USDC",
        "price": 25.0,  # Example price
        "size": 0.01,   # Very small amount (0.01 SOL)
        "confidence": 0.95,
        "timestamp": datetime.now().isoformat()
    }

    logger.info(f"Created test signal: {json.dumps(test_signal, indent=2)}")

    # Build transaction
    logger.info("Building transaction...")
    tx_message = builder.build_swap_tx(test_signal)

    if not tx_message:
        logger.error("Failed to build transaction")
        return False

    logger.info("Transaction built successfully")

    # In a real implementation, you would sign the transaction with the wallet's private key
    # For this test, we'll just simulate the execution

    # Check wallet balance before trade
    logger.info("Checking wallet balance...")
    client = HeliusClient()

    try:
        balance_result = await client.get_balance(wallet_address)
        if balance_result['success']:
            logger.info(f"Wallet balance before trade: {balance_result['balance_sol']:.6f} SOL")
        else:
            logger.error(f"Failed to get wallet balance: {balance_result.get('error')}")
            return False
    finally:
        await client.close()

    # In DRY_RUN mode, we don't actually execute the transaction
    # Instead, we simulate the execution
    if os.getenv("DRY_RUN", "true").lower() == "true":
        logger.info("DRY_RUN mode enabled, simulating transaction execution...")

        # Simulate successful execution
        execution_result = {
            'success': True,
            'signature': 'SIMULATED_TRANSACTION_SIGNATURE',
            'provider': 'helius',
            'response_time': 0.5
        }
    else:
        logger.warning("LIVE MODE ENABLED - THIS WILL EXECUTE A REAL TRANSACTION")
        logger.warning("This feature is not implemented in the test script")
        logger.warning("Aborting to prevent unintended transactions")
        return False

    # Log execution result
    if execution_result['success']:
        logger.info(f"Transaction executed successfully: {execution_result['signature']}")
        logger.info(f"Provider: {execution_result['provider']}")
        logger.info(f"Response time: {execution_result['response_time']:.2f} seconds")
    else:
        logger.error(f"Transaction execution failed: {execution_result.get('error')}")
        return False

    # Save execution result to file
    result_file = output_dir / "test_trade_result.json"
    with open(result_file, "w") as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "signal": test_signal,
            "execution_result": execution_result,
            "dry_run": os.getenv("DRY_RUN", "true").lower() == "true"
        }, f, indent=2)

    logger.info(f"Test trade result saved to {result_file}")

    # Close executor
    await executor.close()

    return True

async def main():
    """Main function."""
    logger.info("Starting test trade...")

    success = await execute_test_trade()

    if success:
        logger.info("Test trade completed successfully")
        return 0
    else:
        logger.error("Test trade failed")
        return 1

if __name__ == "__main__":
    # Set environment variables for testing if not already set
    if not os.getenv("HELIUS_API_KEY"):
        os.environ["HELIUS_API_KEY"] = "dda9f776-9a40-447d-9ca4-22a27c21169e"

    if not os.getenv("WALLET_ADDRESS"):
        os.environ["WALLET_ADDRESS"] = "J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz"

    # Default to DRY_RUN mode for safety
    if not os.getenv("DRY_RUN"):
        os.environ["DRY_RUN"] = "true"

    # Run the test
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
