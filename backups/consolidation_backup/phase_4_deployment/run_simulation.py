#!/usr/bin/env python3
"""
Simulation script for the Synergy7 Trading System.

This script runs a simulation of the Synergy7 Trading System with all components,
including the enhanced momentum strategy, filters, signal enrichment, and RL data collection.
"""

import os
import sys
import time
import signal
import logging
import asyncio
import subprocess
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

# Add parent directory to path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('run_simulation')

# Load simulation environment variables
def load_simulation_env():
    """Load simulation environment variables."""
    env_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env.simulation')
    if os.path.exists(env_file):
        logger.info(f"Loading environment variables from {env_file}")
        load_dotenv(env_file)
        return True
    else:
        logger.error(f"Environment file not found: {env_file}")
        return False

# Start the Streamlit dashboard
def start_streamlit_dashboard():
    """Start the Streamlit dashboard."""
    dashboard_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
        "monitoring",
        "streamlit_dashboard.py"
    )

    if not os.path.exists(dashboard_path):
        logger.error(f"Dashboard file not found: {dashboard_path}")
        return None

    logger.info(f"Starting Streamlit dashboard: {dashboard_path}")

    # Start the dashboard in a separate process
    process = subprocess.Popen(
        ["streamlit", "run", dashboard_path, "--server.port", "8501", "--server.headless", "false"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )

    logger.info(f"Streamlit dashboard started with PID {process.pid}")
    return process

# Start the live trading script
def start_live_trading():
    """Start the live trading script."""
    script_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
        "start_live_trading.py"
    )

    if not os.path.exists(script_path):
        logger.error(f"Live trading script not found: {script_path}")
        return None

    logger.info(f"Starting live trading script: {script_path}")

    # Start the live trading script in a separate process
    process = subprocess.Popen(
        [sys.executable, script_path],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        env=os.environ.copy()
    )

    logger.info(f"Live trading script started with PID {process.pid}")
    return process

# Stop a process
def stop_process(process, name):
    """Stop a process."""
    if process:
        logger.info(f"Stopping {name} (PID {process.pid})...")
        process.terminate()
        try:
            process.wait(timeout=5)
            logger.info(f"{name} stopped")
        except subprocess.TimeoutExpired:
            logger.warning(f"{name} did not terminate, killing it")
            process.kill()
            process.wait()
            logger.info(f"{name} killed")

# Run the simulation
async def run_simulation(duration_seconds=60, filter_chain=None, signal_enricher=None, rl_data_collector=None):
    """
    Run the simulation.

    Args:
        duration_seconds: Duration of the simulation in seconds
        filter_chain: Filter chain for signal filtering
        signal_enricher: Signal enricher for signal enrichment
        rl_data_collector: RL data collector for RL data collection
    """
    logger.info(f"Starting simulation for {duration_seconds} seconds")

    # Log component status
    if filter_chain:
        logger.info(f"Using filter chain with {len(filter_chain.filters)} filters")
    else:
        logger.info("No filter chain provided, signals will not be filtered")

    if signal_enricher:
        logger.info(f"Using signal enricher with algorithm: {signal_enricher.ranking_algorithm}")
    else:
        logger.info("No signal enricher provided, signals will not be enriched")

    if rl_data_collector:
        logger.info(f"Using RL data collector with data collection: {rl_data_collector.data_collection}")
    else:
        logger.info("No RL data collector provided, RL data will not be collected")

    # Load simulation environment variables
    if not load_simulation_env():
        logger.error("Failed to load simulation environment variables")
        return False

    # Start the Streamlit dashboard
    dashboard_process = start_streamlit_dashboard()

    # Start the live trading script with new components
    env = os.environ.copy()
    env["SIMULATION_MODE"] = "true"

    # Create command with arguments for new components
    cmd = [sys.executable, os.path.join(os.path.dirname(os.path.abspath(__file__)), "start_live_trading.py")]

    # Add flags for components
    if filter_chain:
        cmd.append("--use-filters")

    if signal_enricher:
        cmd.append("--use-signal-enricher")

    if rl_data_collector:
        cmd.append("--use-rl-data-collector")

    # Start the live trading script
    logger.info(f"Starting live trading script with command: {' '.join(cmd)}")
    trading_process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        env=env
    )

    logger.info(f"Live trading script started with PID {trading_process.pid}")

    # Wait for the specified duration
    try:
        logger.info(f"Simulation running for {duration_seconds} seconds...")

        # Instead of just sleeping, periodically check processes and log status
        start_time = time.time()
        while time.time() - start_time < duration_seconds:
            # Check if processes are still running
            if trading_process.poll() is not None:
                logger.error(f"Live trading script exited unexpectedly with code {trading_process.returncode}")
                # Get the output
                stdout, stderr = trading_process.communicate()
                logger.error(f"Live trading script stderr: {stderr}")
                break

            # Wait a bit before checking again
            await asyncio.sleep(5)

            # Log periodic status
            elapsed = time.time() - start_time
            logger.info(f"Simulation running for {elapsed:.1f}/{duration_seconds} seconds...")

    except KeyboardInterrupt:
        logger.info("Simulation interrupted by user")

    # Stop the processes
    stop_process(trading_process, "live trading script")
    stop_process(dashboard_process, "Streamlit dashboard")

    # Clean up components
    if filter_chain:
        try:
            from phase_4_deployment.filters.filter_factory import FilterFactory
            await FilterFactory.close_filters(filter_chain)
            logger.info("Filter chain closed")
        except Exception as e:
            logger.error(f"Error closing filter chain: {str(e)}")

    if rl_data_collector:
        try:
            rl_data_collector.clear_memory()
            logger.info("RL data collector memory cleared")
        except Exception as e:
            logger.error(f"Error clearing RL data collector memory: {str(e)}")

    logger.info("Simulation completed")
    return True

# Main function
async def main_async():
    """Async main function."""
    import argparse

    parser = argparse.ArgumentParser(description='Run a simulation of the Synergy7 Trading System')
    parser.add_argument('--duration', '-d', type=int, default=60,
                        help='Duration of the simulation in seconds (default: 60)')
    parser.add_argument('--use-filters', action='store_true',
                        help='Use filter chain for signal filtering')
    parser.add_argument('--use-signal-enricher', action='store_true',
                        help='Use signal enricher for signal enrichment')
    parser.add_argument('--use-rl-data-collector', action='store_true',
                        help='Use RL data collector for RL data collection')
    args = parser.parse_args()

    # Initialize components if requested
    filter_chain = None
    signal_enricher = None
    rl_data_collector = None

    if args.use_filters:
        try:
            from phase_4_deployment.filters.filter_factory import FilterFactory
            # Load config
            import yaml
            with open(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.yaml'), 'r') as f:
                config = yaml.safe_load(f)
            filter_chain = FilterFactory.create_filter_chain(config.get('filters', {}))
            logger.info(f"Initialized filter chain with {len(filter_chain.filters)} filters")
        except Exception as e:
            logger.error(f"Error initializing filter chain: {str(e)}")

    if args.use_signal_enricher:
        try:
            from phase_4_deployment.signal_generator.signal_enricher import SignalEnricher
            # Load config
            import yaml
            with open(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.yaml'), 'r') as f:
                config = yaml.safe_load(f)
            signal_enricher = SignalEnricher(config.get('signal_enrichment', {}))
            logger.info(f"Initialized signal enricher with algorithm: {signal_enricher.ranking_algorithm}")
        except Exception as e:
            logger.error(f"Error initializing signal enricher: {str(e)}")

    if args.use_rl_data_collector:
        try:
            from phase_4_deployment.rl_agent.data_collector import RLDataCollector
            # Load config
            import yaml
            with open(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.yaml'), 'r') as f:
                config = yaml.safe_load(f)
            rl_data_collector = RLDataCollector(config.get('rl_agent', {}))
            logger.info(f"Initialized RL data collector with data collection: {rl_data_collector.data_collection}")
        except Exception as e:
            logger.error(f"Error initializing RL data collector: {str(e)}")

    success = await run_simulation(
        args.duration,
        filter_chain=filter_chain,
        signal_enricher=signal_enricher,
        rl_data_collector=rl_data_collector
    )
    return 0 if success else 1

def main():
    """Main function wrapper for asyncio."""
    return asyncio.run(main_async())

if __name__ == "__main__":
    sys.exit(main())
